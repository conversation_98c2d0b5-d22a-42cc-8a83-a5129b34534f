=== TERMINAL OUTPUT LOG ===
Script: 01_intake_and_nzb_search
Started: 2025-09-23 21:29:03
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search.log
==================================================

[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 📝 Terminal logging started for 01_intake_and_nzb_search
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search.log
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-23 21:29:03
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDERR] [+0:00:00] 2025-09-23 21:29:03,593 - interactive_pipeline_01 - INFO - ===== Starting Interactive Pipeline 01 Execution =====
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDERR] [+0:00:00] 2025-09-23 21:29:03,595 - interactive_pipeline_01 - INFO - Settings loaded successfully
[2025-09-23 21:29:03] [STDERR] [+0:00:00] 2025-09-23 21:29:03,595 - interactive_pipeline_01 - INFO - Configuration: max_candidates=50, quality_fallback=True, telemetry_verbose=False
[2025-09-23 21:29:03] [STDERR] [+0:00:00] 2025-09-23 21:29:03,595 - interactive_pipeline_01 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-23 21:29:03] [STDERR] [+0:00:00] 2025-09-23 21:29:03,595 - interactive_pipeline_01 - INFO - 🔬 Enhanced telemetry integration initialized
[2025-09-23 21:29:03] [STDERR] [+0:00:00] 2025-09-23 21:29:03,595 - interactive_pipeline_01 - INFO -    📊 Loaded 6 existing movie records
[2025-09-23 21:29:03] [STDERR] [+0:00:00] 2025-09-23 21:29:03,595 - interactive_pipeline_01 - INFO - 🔬 Real-time telemetry initialized EARLY - ready for immediate monitoring
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 🔬 Real-time download monitoring enabled (dashboard mode) - will start monitoring as soon as first download begins
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] ============================================================
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 🎬📺 PlexMovieAutomator - Interactive Content Selection
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] ============================================================
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] What type of content would you like to process?
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDOUT] [+0:00:00]   1. Movies only
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDOUT] [+0:00:00]   2. TV Shows only
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDOUT] [+0:00:00]   3. Both Movies and TV Shows
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:03] [STDOUT] [+0:00:00]   4. Quit
[2025-09-23 21:29:03] [STDOUT] [+0:00:00] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] ============================================================
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 🤖 Processing Mode Selection
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] ============================================================
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] How would you like to handle download decisions?
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01]   1. 🖱️  Manual Mode - Choose options for each movie/show individually
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01]   2. 🤖 Full Auto Mode - Automatically use preflight analysis with max candidates
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 📝 Full Auto Mode Details:
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01]    • Automatically selects preflight analysis for every item
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01]    • Automatically chooses max candidates when prompted
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01]    • No manual intervention required - perfect for overnight processing
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:05] [STDOUT] [+0:00:01]    • Falls back gracefully if preflight fails
[2025-09-23 21:29:05] [STDOUT] [+0:00:01] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] ✅ Manual Mode selected - you'll be prompted for each item
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 📁 Loaded 13 tv_shows from C:\Users\<USER>\Videos\PlexAutomator\new_tv_requests.txt
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] ======================================================================
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 📺 TV Shows Available for Processing:
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] ======================================================================
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]    1. Ed, Edd n Eddy (1999)                    📚 Complete Series        
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]       📋 Will use TVDB for chronological episode tracking
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]    2. Adventure Time (2010)                    📚 Complete Series        
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]       📋 Will use TVDB for chronological episode tracking
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]    3. The Saddle Club (2003)                   📚 Complete Series        
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]       📋 Will use TVDB for chronological episode tracking
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]    4. Samurai Jack (2001) S01                  📀 Season S01             
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]       📋 Will use TVDB for chronological episode tracking
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]    5. Steven Universe (2013) S02               📀 Season S02             
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]       📋 Will use TVDB for chronological episode tracking
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]    6. Futurama (1999) S01                      📀 Season S01             
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]       📋 Will use TVDB for chronological episode tracking
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]    7. The Powerpuff Girls (1998)               📚 Complete Series        
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]       📋 Will use TVDB for chronological episode tracking
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]    8. Regular Show (2010) S08E31               📺 Episode S08E31         
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]    9. Dexter's Laboratory (1996) S01E01, S01E05, S01E12 📺 Multi-Episodes S01E01, S01E05, S01E12
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]   10. Batman: The Brave and the Bold (2008) S01E01 📺 Episode S01E01         
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]   11. Teen Titans (2003) S02E03, S02E07, S02E13 📺 Multi-Episodes S02E03, S02E07, S02E13
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]   12. Johnny Bravo (1997) S01E01, S03E15       📺 Multi-Episodes S01E01, S03E15
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]   13. Ben 10 (2005) S01E01, S02E13, S04E21     📺 Multi-Episodes S01E01, S02E13, S04E21
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 📊 Legend:
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]    📺 Episode    - Single episode download
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]    📀 Season     - Full season download (all episodes)
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]    📚 Series     - Complete series (all seasons)
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 📝 Selection Options:
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]   • Single: Enter number (e.g., '3')
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]   • Multiple: Enter comma-separated numbers (e.g., '1,3,5')
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]   • All: Enter 'all' or 'a'
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]   • None: Enter 'none' or 'n' to skip
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:08] [STDOUT] [+0:00:04]   • Quit: Enter 'quit' or 'q'
[2025-09-23 21:29:08] [STDOUT] [+0:00:04] 
[2025-09-23 21:29:23] [STDOUT] [+0:00:19] 
[2025-09-23 21:29:23] [STDOUT] [+0:00:19] ✅ Selected 1 TV shows:
[2025-09-23 21:29:23] [STDOUT] [+0:00:19] 
[2025-09-23 21:29:23] [STDOUT] [+0:00:19]     1. 📚 Batman: The Brave and the Bold (2008) S01E01
[2025-09-23 21:29:23] [STDOUT] [+0:00:19] 
[2025-09-23 21:29:25] [STDOUT] [+0:00:21] 
[2025-09-23 21:29:25] [STDOUT] [+0:00:21] 📺 Processing 1 selected TV shows...
[2025-09-23 21:29:25] [STDOUT] [+0:00:21] 
[2025-09-23 21:29:25] [STDOUT] [+0:00:21] ============================================================
[2025-09-23 21:29:25] [STDOUT] [+0:00:21] 
[2025-09-23 21:29:25] [STDERR] [+0:00:21] 2025-09-23 21:29:25,159 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-23 21:29:25] [STDOUT] [+0:00:21] 
[2025-09-23 21:29:25] [STDOUT] [+0:00:21] 📍 Progress: 1/1
[2025-09-23 21:29:25] [STDOUT] [+0:00:21] 
[2025-09-23 21:29:25] [STDOUT] [+0:00:21] 📺 Processing: Batman: The Brave and the Bold (2008) S01E01
[2025-09-23 21:29:25] [STDOUT] [+0:00:21] 
[2025-09-23 21:29:25] [STDOUT] [+0:00:21]    🎯 Request Type: Specific Episodes
[2025-09-23 21:29:25] [STDOUT] [+0:00:21] 
[2025-09-23 21:29:25] [STDOUT] [+0:00:21]    📺 Target: Season 1, Episode 1
[2025-09-23 21:29:25] [STDOUT] [+0:00:21] 
[2025-09-23 21:29:25] [STDERR] [+0:00:21] 2025-09-23 21:29:25,163 - interactive_pipeline_01 - INFO - Processing TV show: Batman: The Brave and the Bold (2008) S01E01 (specific_episodes)
[2025-09-23 21:29:25] [STDERR] [+0:00:21] 2025-09-23 21:29:25,446 - _internal.src.metadata_fetcher - INFO - TMDb TV search with year 2008: 1 results
[2025-09-23 21:29:25] [STDERR] [+0:00:21] 2025-09-23 21:29:25,515 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'batman:' -> 'batman'
[2025-09-23 21:29:25] [STDERR] [+0:00:21] 2025-09-23 21:29:25,581 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'batman:' -> 'batman'
[2025-09-23 21:29:25] [STDERR] [+0:00:21] 2025-09-23 21:29:25,583 - _internal.utils.fuzzy_matching - INFO - Found 1 exact matches for 'Batman: The Brave and the Bold', prioritizing them
[2025-09-23 21:29:25] [STDERR] [+0:00:22] 2025-09-23 21:29:25,668 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'batman:' -> 'batman'
[2025-09-23 21:29:25] [STDERR] [+0:00:22] 2025-09-23 21:29:25,739 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'batman:' -> 'batman'
[2025-09-23 21:29:25] [STDERR] [+0:00:22] 2025-09-23 21:29:25,837 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'batman:' -> 'batman'
[2025-09-23 21:29:25] [STDERR] [+0:00:22] 2025-09-23 21:29:25,930 - _internal.utils.fuzzy_matching - INFO - Spell corrections applied: 'batman:' -> 'batman'
[2025-09-23 21:29:25] [STDERR] [+0:00:22] 2025-09-23 21:29:25,952 - _internal.src.metadata_fetcher - ERROR - Error during TV fuzzy matching for 'Batman: The Brave and the Bold': EnhancedFuzzyMatchingConfig.get_year_tolerance() missing 1 required positional argument: 'content_type'
[2025-09-23 21:29:26] [STDOUT] [+0:00:22] ✅ Found metadata: Batman: The Brave and the Bold (2008)
[2025-09-23 21:29:26] [STDOUT] [+0:00:22] 
[2025-09-23 21:29:26] [STDERR] [+0:00:22] 2025-09-23 21:29:26,009 - interactive_pipeline_01 - INFO - Successfully found TV metadata for: Batman: The Brave and the Bold
[2025-09-23 21:29:26] [STDERR] [+0:00:22] 2025-09-23 21:29:26,544 - interactive_pipeline_01 - INFO - 🧪 Skipping creation of season pack blocking profile (preflight handles pack decisions)
[2025-09-23 21:29:26] [STDERR] [+0:00:22] 2025-09-23 21:29:26,544 - interactive_pipeline_01 - INFO - 📺 TV Quality Strategy (ADAPTIVE): Adaptive Quality: Using inclusive profile (ID 6) - prevents episode skipping, allows best available quality selection
[2025-09-23 21:29:26] [STDERR] [+0:00:22] 2025-09-23 21:29:26,544 - interactive_pipeline_01 - INFO - 📺 Year-based preference hint: 2008 (used for internal logic, not restrictions)
[2025-09-23 21:29:26] [STDERR] [+0:00:22] 2025-09-23 21:29:26,544 - interactive_pipeline_01 - INFO - Searching Sonarr for: Batman: The Brave and the Bold 2008
[2025-09-23 21:29:32] [STDERR] [+0:00:28] 2025-09-23 21:29:32,276 - interactive_pipeline_01 - INFO - Selected TV show: Batman: The Brave and the Bold (2008) | tvdbId=82824 | alternatives: [{'title': 'Der Bergdoktor (2008)', 'year': 2008, 'tvdbId': 195681, 'score': 357}, {'title': 'Merlin', 'year': 2008, 'tvdbId': 83123, 'score': 318}, {'title': 'Killer Women (2008)', 'year': 2008, 'tvdbId': 86471, 'score': 315}]
[2025-09-23 21:29:32] [STDERR] [+0:00:28] 2025-09-23 21:29:32,282 - interactive_pipeline_01 - INFO - 📁 Matched existing Sonarr root folder: E:\
[2025-09-23 21:29:32] [STDERR] [+0:00:28] 2025-09-23 21:29:32,283 - interactive_pipeline_01 - INFO - 📋 Adaptive Quality: Using inclusive profile (ID 6) - prevents episode skipping, allows best available quality selection
[2025-09-23 21:29:32] [STDERR] [+0:00:28] 2025-09-23 21:29:32,283 - interactive_pipeline_01 - INFO - 📺 Adding TV show to Sonarr with 1 quality profile(s): Batman: The Brave and the Bold
[2025-09-23 21:29:32] [STDERR] [+0:00:28] 2025-09-23 21:29:32,285 - interactive_pipeline_01 - INFO -    📥 Adding with quality profile 6...
[2025-09-23 21:29:32] [STDERR] [+0:00:29] 2025-09-23 21:29:32,606 - interactive_pipeline_01 - INFO -    ✅ Added: Batman: The Brave and the Bold (Series ID 444, Profile 6)
[2025-09-23 21:29:32] [STDERR] [+0:00:29] 2025-09-23 21:29:32,606 - interactive_pipeline_01 - INFO -    📺 Configuring monitoring for 1 specific episodes
[2025-09-23 21:29:32] [STDERR] [+0:00:29] 2025-09-23 21:29:32,606 - interactive_pipeline_01 - INFO -    🔄 Attempt 1/15: Fetching episodes for series 444
[2025-09-23 21:29:32] [STDERR] [+0:00:29] 2025-09-23 21:29:32,609 - interactive_pipeline_01 - INFO -    📊 Found 66 episodes in Sonarr
[2025-09-23 21:29:32] [STDERR] [+0:00:29] 2025-09-23 21:29:32,609 - interactive_pipeline_01 - INFO -    📋 Found 66 episodes in series
[2025-09-23 21:29:32] [STDERR] [+0:00:29] 2025-09-23 21:29:32,609 - interactive_pipeline_01 - INFO -    🎯 Target episodes: {(1, 1)}
[2025-09-23 21:29:32] [STDERR] [+0:00:29] 2025-09-23 21:29:32,609 - interactive_pipeline_01 - INFO -       ✓ Will monitor S01E01
[2025-09-23 21:29:32] [STDERR] [+0:00:29] 2025-09-23 21:29:32,609 - interactive_pipeline_01 - INFO -    📝 Configuring monitoring for 1 specific episodes
[2025-09-23 21:29:32] [STDERR] [+0:00:29] 2025-09-23 21:29:32,611 - interactive_pipeline_01 - WARNING - Failed to update series monitoring via client
[2025-09-23 21:29:32] [STDERR] [+0:00:29] 2025-09-23 21:29:32,668 - interactive_pipeline_01 - INFO -    ✅ Successfully configured monitoring for 1 episodes
[2025-09-23 21:29:32] [STDERR] [+0:00:29] 2025-09-23 21:29:32,668 - interactive_pipeline_01 - INFO -    ✅ Episode monitoring configuration complete
[2025-09-23 21:29:32] [STDERR] [+0:00:29] 2025-09-23 21:29:32,669 - interactive_pipeline_01 - INFO -    ⏭️ Skipping immediate search triggers for 1 episodes (preflight will handle searches)
[2025-09-23 21:29:32] [STDERR] [+0:00:29] 2025-09-23 21:29:32,669 - interactive_pipeline_01 - INFO - 📏 Max episode size threshold configured: 40.0 GB
[2025-09-23 21:29:37] [STDERR] [+0:00:34] 2025-09-23 21:29:37,681 - interactive_pipeline_01 - INFO - Episode size enforcement: no oversized items detected
[2025-09-23 21:29:37] [STDERR] [+0:00:34] 2025-09-23 21:29:37,681 - interactive_pipeline_01 - INFO - ✅ Season pack evaluation handled by preflight analyzer
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 📥 Queued "Batman: The Brave and the Bold (2008)" for download...
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDERR] [+0:00:34] 2025-09-23 21:29:37,682 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-23T21:29:37.682309", "event": "download_queued", "job_id": "sonarr_444_series", "title": "Batman: The Brave and the Bold (2008)", "source": "sonarr", "status": "pending", "progress": 0.0, "size_total": 0, "size_downloaded": 0, "speed_bps": 0.0, "eta": "Unknown", "sonarr_id": 444, "quality": "Unknown"}
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 📊 TV show queued for download: Batman: The Brave and the Bold (2008)
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDOUT] [+0:00:34]    🔬 Real-time tracking: sonarr_4...
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDOUT] [+0:00:34]    📺 Series-wide tracking enabled
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDERR] [+0:00:34] 2025-09-23 21:29:37,682 - interactive_pipeline_01 - INFO - Telemetry job started: sonarr_444_series for series 444
[2025-09-23 21:29:37] [STDOUT] [+0:00:34]    📺 Configured for: S01E01 only
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 🔍 Alternative candidate matches (top scoring):
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDOUT] [+0:00:34]    • Der Bergdoktor (2008) (2008) tvdb:195681 score:357
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDOUT] [+0:00:34]    • Merlin (2008) tvdb:83123 score:318
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDOUT] [+0:00:34]    • Killer Women (2008) (2008) tvdb:86471 score:315
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDERR] [+0:00:34] 2025-09-23 21:29:37,682 - interactive_pipeline_01 - INFO - ✅ Successfully added TV show to Sonarr: Batman: The Brave and the Bold
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 🤔 Download Strategy Choice for: Batman: The Brave and the Bold
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] Choose how you want to handle downloads for this show:
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 2. ⚡ Sonarr Auto-Grab - Let Sonarr immediately search and grab based on quality profiles
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 3. ⏭️  Skip - Add to Sonarr but don't start any downloads yet
[2025-09-23 21:29:37] [STDOUT] [+0:00:34] 
[2025-09-23 21:30:02] [STDOUT] [+0:00:59] 🔬 Using Preflight Analysis for Batman: The Brave and the Bold
[2025-09-23 21:30:02] [STDOUT] [+0:00:59] 
[2025-09-23 21:30:02] [STDERR] [+0:00:59] 2025-09-23 21:30:02,878 - interactive_pipeline_01 - INFO - User selected preflight analysis for: Batman: The Brave and the Bold
[2025-09-23 21:30:03] [STDOUT] [+0:00:59] 🔎 Preflight analyzing 1 season(s): [1]
[2025-09-23 21:30:03] [STDOUT] [+0:00:59] 
[2025-09-23 21:30:03] [STDOUT] [+0:00:59]    Season 1: Episodes [1]
[2025-09-23 21:30:03] [STDOUT] [+0:00:59] 
[2025-09-23 21:30:11] [STDOUT] [+0:01:07] 
[2025-09-23 21:30:11] [STDOUT] [+0:01:07] 🎯 Analyzing Season 1...
[2025-09-23 21:30:11] [STDOUT] [+0:01:07] 
[2025-09-23 21:30:11] [STDOUT] [+0:01:07] 🆕 No existing decision found for Season 1, running fresh analysis
[2025-09-23 21:30:11] [STDOUT] [+0:01:07] 
[2025-09-23 21:30:11] [STDERR] [+0:01:07] 2025-09-23 21:30:11,418 - preflight_analyzer.tv_show_preflight_selector - INFO - 🎬 Starting TV preflight analysis - Series: 444, Episodes: 1, Mode: reliability
[2025-09-23 21:30:11] [STDERR] [+0:01:07] 2025-09-23 21:30:11,418 - preflight_analyzer.tv_show_preflight_selector - INFO - 📋 Analysis configuration - Cache: False, Deduplicate: False, Fresh checks: False
[2025-09-23 21:30:11] [STDERR] [+0:01:07] 2025-09-23 21:30:11,419 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔧 DEBUG: preflight_tv_show called with:
[2025-09-23 21:30:11] [STDERR] [+0:01:07] 2025-09-23 21:30:11,419 - preflight_analyzer.tv_show_preflight_selector - INFO -    series_id: 444
[2025-09-23 21:30:11] [STDERR] [+0:01:07] 2025-09-23 21:30:11,419 - preflight_analyzer.tv_show_preflight_selector - INFO -    config_path: config\preflight_config.json
[2025-09-23 21:30:11] [STDERR] [+0:01:07] 2025-09-23 21:30:11,419 - preflight_analyzer.tv_show_preflight_selector - INFO -    mode: reliability
[2025-09-23 21:30:11] [STDERR] [+0:01:07] 2025-09-23 21:30:11,419 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: Loading config from: C:\Users\<USER>\Videos\PlexAutomator\config\preflight_config.json
[2025-09-23 21:30:11] [STDERR] [+0:01:07] 2025-09-23 21:30:11,419 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: Config file exists: True
[2025-09-23 21:30:11] [STDERR] [+0:01:07] 2025-09-23 21:30:11,419 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: Current working directory: C:\Users\<USER>\Videos\PlexAutomator
[2025-09-23 21:30:11] [STDERR] [+0:01:07] 2025-09-23 21:30:11,420 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: Loaded config keys: ['sonarr', 'radarr', 'indexers', 'franchise_exclusions']
[2025-09-23 21:30:11] [STDERR] [+0:01:07] 2025-09-23 21:30:11,439 - preflight_analyzer.tv_show_preflight_selector - INFO - 📺 Analyzing 1 episodes from Batman: The Brave and the Bold Season 1
[2025-09-23 21:30:11] [STDERR] [+0:01:07] 2025-09-23 21:30:11,439 - preflight_analyzer.tv_show_preflight_selector - INFO - 📺 Analyzing 1 episodes in parallel (max 6 concurrent)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,957 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: Before filtering - found 7 raw releases for Batman: The Brave and the Bold
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,958 - preflight_analyzer.tv_show_preflight_selector - INFO -    Raw release 1: Batman.The.Brave.and.the.Bold.S01E01.CUSTOM.MULTi.1080p.BluRay.REMUX.AVC.DTS-HD.MA.2.0-HeavyWeight (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,958 - preflight_analyzer.tv_show_preflight_selector - INFO -    Raw release 2: Batman.The.Brave.and.The.Bold.S01E01.1080p.BluRay.x264-DEiMOS (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,958 - preflight_analyzer.tv_show_preflight_selector - INFO -    Raw release 3: Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.BluRay.DDP.2.0.H.265.-EDGE2020 (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,958 - preflight_analyzer.tv_show_preflight_selector - INFO -    Raw release 4: Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.HMAX.WEB-DL.DD.2.0.H.264.-EDGE2020 (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,958 - preflight_analyzer.tv_show_preflight_selector - INFO -    Raw release 5: Batman.The.Brave.and.the.Bold.S01E01.Rise.of.the.Blue.Beetle.1080p.MAX.WEB-DL.DDP2.0.H.264-Kitsune (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,958 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔧 DEBUG: Checking series ID information in releases...
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,958 - preflight_analyzer.tv_show_preflight_selector - INFO -    Release 1 IDs: seriesId=None, tvdbId=82824, episodeId=None
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,958 - preflight_analyzer.tv_show_preflight_selector - INFO -    Release 2 IDs: seriesId=None, tvdbId=82824, episodeId=None
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,959 - preflight_analyzer.tv_show_preflight_selector - INFO -    Release 3 IDs: seriesId=None, tvdbId=82824, episodeId=None
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,959 - preflight_analyzer.tv_show_preflight_selector - INFO - 🧠 UNIFIED FILTERING: Processing 7 releases for 'Batman: The Brave and the Bold'
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,959 - preflight_analyzer.tv_show_preflight_selector - INFO - 🧠 UNIFIED FILTERING: Series ID: 444, TVDB ID: 82824
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,959 - preflight_analyzer.tv_hardening_helpers - INFO - 🧠 DYNAMIC DETECTION: Auto-detected conflicts: ['structure_mismatch', 'length_mismatch']
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,959 - preflight_analyzer.tv_hardening_helpers - INFO - 🔍 HARDENING DEBUG: target_series_title='Batman: The Brave and the Bold', target_norm='batman the brave and the bold'
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,959 - preflight_analyzer.tv_hardening_helpers - INFO - 🔍 HARDENING DEBUG: sonarr_series_id=444, tvdb_series_id=82824
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,959 - preflight_analyzer.tv_hardening_helpers - INFO - 🔍 HARDENING DEBUG: banned_substrings=['structure_mismatch', 'length_mismatch']
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO - 🔍 HARDENING DEBUG: banned (normalized)=['structure mismatch', 'length mismatch']
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO - 🔍 HARDENING DEBUG: Processing 7 candidates
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO - 🔍 HARDENING DEBUG: Candidate 1: 'Batman.The.Brave.and.the.Bold.S01E01.CUSTOM.MULTi.1080p.BluRay.REMUX.AVC.DTS-HD.MA.2.0-HeavyWeight' (series_id=444, tvdb_id=82824)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO -    🔍 BAN CHECK (priority): checking 'batman the brave and the bold s01e01 custom multi 1080p bluray remux avc dts hd ma 2 0 heavyweight' against banned: ['structure mismatch', 'length mismatch']
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ BAN PASS: no banned substrings found
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ ID MATCH: series_id 444 == 444 (after ban check)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO - 🔍 HARDENING DEBUG: Candidate 2: 'Batman.The.Brave.and.The.Bold.S01E01.1080p.BluRay.x264-DEiMOS' (series_id=444, tvdb_id=82824)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO -    🔍 BAN CHECK (priority): checking 'batman the brave and the bold s01e01 1080p bluray x264 deimos' against banned: ['structure mismatch', 'length mismatch']
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ BAN PASS: no banned substrings found
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ ID MATCH: series_id 444 == 444 (after ban check)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO - 🔍 HARDENING DEBUG: Candidate 3: 'Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.BluRay.DDP.2.0.H.265.-EDGE2020' (series_id=444, tvdb_id=82824)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO -    🔍 BAN CHECK (priority): checking 'batman the brave and the bold s01e01 the rise of the blue beetle 1080p bluray ddp 2 0 h 265 edge2020' against banned: ['structure mismatch', 'length mismatch']
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ BAN PASS: no banned substrings found
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,960 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ ID MATCH: series_id 444 == 444 (after ban check)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO - 🔍 HARDENING DEBUG: Candidate 4: 'Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.HMAX.WEB-DL.DD.2.0.H.264.-EDGE2020' (series_id=444, tvdb_id=82824)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO -    🔍 BAN CHECK (priority): checking 'batman the brave and the bold s01e01 the rise of the blue beetle 1080p hmax web dl dd 2 0 h 264 edge2020' against banned: ['structure mismatch', 'length mismatch']
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ BAN PASS: no banned substrings found
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ ID MATCH: series_id 444 == 444 (after ban check)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO - 🔍 HARDENING DEBUG: Candidate 5: 'Batman.The.Brave.and.the.Bold.S01E01.Rise.of.the.Blue.Beetle.1080p.MAX.WEB-DL.DDP2.0.H.264-Kitsune' (series_id=444, tvdb_id=82824)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO -    🔍 BAN CHECK (priority): checking 'batman the brave and the bold s01e01 rise of the blue beetle 1080p max web dl ddp2 0 h 264 kitsune' against banned: ['structure mismatch', 'length mismatch']
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ BAN PASS: no banned substrings found
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ ID MATCH: series_id 444 == 444 (after ban check)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO - 🔍 HARDENING DEBUG: Candidate 6: 'Batman.The.Brave.and.the.Bold.S01E01.Rise.of.the.Blue.Beetle.720p.BluRay.DD2.0.x264-ViSUM' (series_id=444, tvdb_id=82824)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO -    🔍 BAN CHECK (priority): checking 'batman the brave and the bold s01e01 rise of the blue beetle 720p bluray dd2 0 x264 visum' against banned: ['structure mismatch', 'length mismatch']
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ BAN PASS: no banned substrings found
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ ID MATCH: series_id 444 == 444 (after ban check)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO - 🔍 HARDENING DEBUG: Candidate 7: 'Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle' (series_id=444, tvdb_id=82824)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO -    🔍 BAN CHECK (priority): checking 'batman the brave and the bold s01e01 rise of the blue beetle' against banned: ['structure mismatch', 'length mismatch']
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ BAN PASS: no banned substrings found
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO -    ✅ ID MATCH: series_id 444 == 444 (after ban check)
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_hardening_helpers - INFO - 🔍 HARDENING DEBUG: Results - ID matched: 7, title matched: 0
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_show_preflight_selector - INFO - 🧠 UNIFIED FILTERING: Kept 7/7 releases after hierarchical filtering
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: After filtering - kept 7 releases for Batman: The Brave and the Bold
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_show_preflight_selector - INFO -    Filtered release 1: Batman.The.Brave.and.the.Bold.S01E01.CUSTOM.MULTi.1080p.BluRay.REMUX.AVC.DTS-HD.MA.2.0-HeavyWeight (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,961 - preflight_analyzer.tv_show_preflight_selector - INFO -    Filtered release 2: Batman.The.Brave.and.The.Bold.S01E01.1080p.BluRay.x264-DEiMOS (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    Filtered release 3: Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.BluRay.DDP.2.0.H.265.-EDGE2020 (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    Filtered release 4: Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.HMAX.WEB-DL.DD.2.0.H.264.-EDGE2020 (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    Filtered release 5: Batman.The.Brave.and.the.Bold.S01E01.Rise.of.the.Blue.Beetle.1080p.MAX.WEB-DL.DDP2.0.H.264-Kitsune (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: Processing 7 filtered releases into BasicRelease objects
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    ✅ Creating BasicRelease: Batman.The.Brave.and.the.Bold.S01E01.CUSTOM.MULTi.1080p.BluRay.REMUX.AVC.DTS-HD.MA.2.0-HeavyWeight (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    ✅ Creating BasicRelease: Batman.The.Brave.and.The.Bold.S01E01.1080p.BluRay.x264-DEiMOS (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    ✅ Creating BasicRelease: Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.BluRay.DDP.2.0.H.265.-EDGE2020 (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    ✅ Creating BasicRelease: Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.HMAX.WEB-DL.DD.2.0.H.264.-EDGE2020 (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    ✅ Creating BasicRelease: Batman.The.Brave.and.the.Bold.S01E01.Rise.of.the.Blue.Beetle.1080p.MAX.WEB-DL.DDP2.0.H.264-Kitsune (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    ✅ Creating BasicRelease: Batman.The.Brave.and.the.Bold.S01E01.Rise.of.the.Blue.Beetle.720p.BluRay.DD2.0.x264-ViSUM (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    ✅ Creating BasicRelease: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle (indexer: NZBFinder (Prowlarr))
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: Created 7 BasicRelease objects
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: Ranked releases - got 7 ranked releases
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: Preparing analysis tasks for 7 ranked releases
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    📋 Creating analysis task for: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    📋 Creating analysis task for: Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.BluRay.DDP.2.0.H.265.-EDGE2020
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    📋 Creating analysis task for: Batman.The.Brave.and.the.Bold.S01E01.Rise.of.the.Blue.Beetle.1080p.MAX.WEB-DL.DDP2.0.H.264-Kitsune
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    📋 Creating analysis task for: Batman.The.Brave.and.the.Bold.S01E01.Rise.of.the.Blue.Beetle.720p.BluRay.DD2.0.x264-ViSUM
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,962 - preflight_analyzer.tv_show_preflight_selector - INFO -    📋 Creating analysis task for: Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.HMAX.WEB-DL.DD.2.0.H.264.-EDGE2020
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,963 - preflight_analyzer.tv_show_preflight_selector - INFO -    📋 Creating analysis task for: Batman.The.Brave.and.The.Bold.S01E01.1080p.BluRay.x264-DEiMOS
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,963 - preflight_analyzer.tv_show_preflight_selector - INFO -    📋 Creating analysis task for: Batman.The.Brave.and.the.Bold.S01E01.CUSTOM.MULTi.1080p.BluRay.REMUX.AVC.DTS-HD.MA.2.0-HeavyWeight
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,963 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: Created 7 analysis tasks
[2025-09-23 21:30:11] [STDERR] [+0:01:08] 2025-09-23 21:30:11,963 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: Starting parallel analysis of 7 tasks
[2025-09-23 21:30:12] [STDERR] [+0:01:08] 2025-09-23 21:30:12,072 - tv_analysis.NZBFinder (Prowlarr) - INFO -    🔍 21:30:12 Analyzing: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle
[2025-09-23 21:30:12] [STDERR] [+0:01:08] 2025-09-23 21:30:12,466 - tv_analysis.NZBFinder (Prowlarr) - INFO -    🔍 21:30:12 Analyzing: Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.BluRay.DDP.2.0.H.265.-EDGE2020
[2025-09-23 21:30:12] [STDERR] [+0:01:09] 2025-09-23 21:30:12,894 - tv_analysis.NZBFinder (Prowlarr) - INFO -    🔍 21:30:12 Analyzing: Batman.The.Brave.and.the.Bold.S01E01.Rise.of.the.Blue.Beetle.1080p.MAX.WEB-DL.DDP2.0.H.264-Kitsune
[2025-09-23 21:30:13] [STDERR] [+0:01:09] 2025-09-23 21:30:13,324 - tv_analysis.NZBFinder (Prowlarr) - INFO -    🔍 21:30:13 Analyzing: Batman.The.Brave.and.the.Bold.S01E01.Rise.of.the.Blue.Beetle.720p.BluRay.DD2.0.x264-ViSUM
[2025-09-23 21:30:13] [STDERR] [+0:01:10] 2025-09-23 21:30:13,880 - tv_analysis.NZBFinder (Prowlarr) - INFO -    🔍 21:30:13 Analyzing: Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.HMAX.WEB-DL.DD.2.0.H.264.-EDGE2020
[2025-09-23 21:30:14] [STDERR] [+0:01:10] 2025-09-23 21:30:14,496 - tv_analysis.NZBFinder (Prowlarr) - INFO -    🔍 21:30:14 Analyzing: Batman.The.Brave.and.The.Bold.S01E01.1080p.BluRay.x264-DEiMOS
[2025-09-23 21:30:47] [STDERR] [+0:01:43] 2025-09-23 21:30:47,049 - tv_analysis.NZBFinder (Prowlarr) - INFO -    ✅ 21:30:47 Result: ACCEPT (risk: 0.0982, missing: 0.5%)
[2025-09-23 21:30:47] [STDERR] [+0:01:43] 2025-09-23 21:30:47,049 - tv_analysis.NZBFinder (Prowlarr) - INFO -    🔍 21:30:47 Analyzing: Batman.The.Brave.and.the.Bold.S01E01.CUSTOM.MULTi.1080p.BluRay.REMUX.AVC.DTS-HD.MA.2.0-HeavyWeight
[2025-09-23 21:30:58] [STDERR] [+0:01:54] 2025-09-23 21:30:58,243 - tv_analysis.NZBFinder (Prowlarr) - INFO -    ✅ 21:30:58 Result: ACCEPT (risk: 0.0479, missing: 0.4%)
[2025-09-23 21:31:00] [STDERR] [+0:01:57] 2025-09-23 21:31:00,774 - tv_analysis.NZBFinder (Prowlarr) - INFO -    ✅ 21:31:00 Result: ACCEPT (risk: 0.0238, missing: 0.0%)
[2025-09-23 21:31:06] [STDERR] [+0:02:03] 2025-09-23 21:31:06,776 - tv_analysis.NZBFinder (Prowlarr) - INFO -    ✅ 21:31:06 Result: ACCEPT (risk: 0.1622, missing: 0.4%)
[2025-09-23 21:31:10] [STDERR] [+0:02:07] 2025-09-23 21:31:10,617 - tv_analysis.NZBFinder (Prowlarr) - INFO -    ✅ 21:31:10 Result: ACCEPT (risk: 0.0426, missing: 0.0%)
[2025-09-23 21:31:11] [STDERR] [+0:02:07] 2025-09-23 21:31:11,423 - tv_analysis.NZBFinder (Prowlarr) - INFO -    ✅ 21:31:11 Result: ACCEPT (risk: 0.0230, missing: 0.0%)
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,118 - tv_analysis.NZBFinder (Prowlarr) - INFO -    ✅ 21:31:47 Result: ACCEPT (risk: 0.0396, missing: 0.0%)
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,119 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: Analysis completed - got 7 results
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,119 - preflight_analyzer.tv_show_preflight_selector - INFO -    📊 Analysis 1 result: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle → ACCEPT
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,119 - preflight_analyzer.tv_show_preflight_selector - INFO -    📊 Analysis 2 result: Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.BluRay.DDP.2.0.H.265.-EDGE2020 → ACCEPT
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,119 - preflight_analyzer.tv_show_preflight_selector - INFO -    📊 Analysis 3 result: Batman.The.Brave.and.the.Bold.S01E01.Rise.of.the.Blue.Beetle.1080p.MAX.WEB-DL.DDP2.0.H.264-Kitsune → ACCEPT
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,119 - preflight_analyzer.tv_show_preflight_selector - INFO -    📊 Analysis 4 result: Batman.The.Brave.and.the.Bold.S01E01.Rise.of.the.Blue.Beetle.720p.BluRay.DD2.0.x264-ViSUM → ACCEPT
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,119 - preflight_analyzer.tv_show_preflight_selector - INFO -    📊 Analysis 5 result: Batman.The.Brave.and.the.Bold.S01E01.The.Rise.of.the.Blue.Beetle.1080p.HMAX.WEB-DL.DD.2.0.H.264.-EDGE2020 → ACCEPT
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,119 - preflight_analyzer.tv_show_preflight_selector - INFO -    📊 Analysis 6 result: Batman.The.Brave.and.The.Bold.S01E01.1080p.BluRay.x264-DEiMOS → ACCEPT
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,119 - preflight_analyzer.tv_show_preflight_selector - INFO -    📊 Analysis 7 result: Batman.The.Brave.and.the.Bold.S01E01.CUSTOM.MULTi.1080p.BluRay.REMUX.AVC.DTS-HD.MA.2.0-HeavyWeight → ACCEPT
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,119 - preflight_analyzer.tv_show_preflight_selector - INFO - 🔍 DEBUG: Episode analysis complete - total results: 7
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,120 - preflight_analyzer.tv_show_preflight_selector - INFO - 🎯 Episode coverage: 1/1 episodes (100.0%)
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,120 - preflight_analyzer.tv_show_preflight_selector - INFO - 🎯 100% episode coverage - using individual episodes only
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,120 - preflight_analyzer.tv_show_preflight_selector - INFO - 🧠 Smart planning selected: 1 items
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,120 - preflight_analyzer.tv_show_preflight_selector - INFO -    📊 Coverage: 1/1 episodes
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,120 - preflight_analyzer.tv_show_preflight_selector - INFO -    🔄 Overlap: 0 episodes
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,120 - preflight_analyzer.tv_show_preflight_selector - INFO -    ❌ Missing: 0 episodes
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,120 - preflight_analyzer.tv_show_preflight_selector - INFO -    🎯 Score: 1.000
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,120 - preflight_analyzer.tv_show_preflight_selector - INFO - 📝 Final plan: Using 1 individual episodes
[2025-09-23 21:31:47] [STDOUT] [+0:02:43]    📊 Episode queued: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle (tracking enabled)
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,487 - preflight_analyzer.tv_show_preflight_selector - INFO -    📞 Callback executed for episode: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,487 - preflight_analyzer.tv_show_preflight_selector - INFO - ✅ TV preflight analysis completed in 96.07s - Strategy: episodes
[2025-09-23 21:31:47] [STDOUT] [+0:02:43]    🔧 DEBUG: Selecting best from 7 candidates for episode 73197
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43]    🔧 NEW LOGIC: Selected Batman.The.Brave.and.the.Bold.S01E01.CUSTOM.M... - 1080p, 3.77GB, 🇺🇸, Other, risk: 0.0396
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43]    🐛 DEBUG: Filtering 0 accepted packs for season 1
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43]    🐛 DEBUG: Found 0 season-specific packs
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43]    🐛 DEBUG: No season-specific packs found, season_pack = None
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43]    ➤ Season 1: 1/1 episodes covered (100.00%) with 7 files, strategy=episodes
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43]    📋 Season 1: 1 downloads handled by immediate callback
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 📊 Combined Results: 1 total episodes analyzed, 1 acceptable episodes + 0 season packs found
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 📊 Queue Status: 1 items queued across 1 season(s) (telemetry tracking enabled)
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 📝 Season 1 decision saved: workspace\preflight_decisions\tv_shows\Batman:_The_Brave_and_the_Bold_S01.json
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] ✅ Preflight found and started downloads for 1 episodes + 0 packs across 1 season(s)
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 🔬 Preflight Episode Selections (already downloading):
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43]    #1. 📺 Batman.The.Brave.and.the.Bold.S01E01.CUSTOM.MULTi.1080p.BluRay.REMUX.AVC.DTS-HD.MA.2.0-HeavyWeight
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43]        💾 Size: 3.77 GB (4,045,417,677 bytes)
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43]        ⚡ Risk: 0.0396 | Missing: 0.0% | Decision: ACCEPT
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 📊 Preflight Summary: 1 episodes + 0 packs | Total: 3.77 GB
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 🎯 Downloads started immediately after each season analysis
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:43]    (No waiting for all analysis to complete - optimal efficiency!)
[2025-09-23 21:31:47] [STDOUT] [+0:02:43] 
[2025-09-23 21:31:47] [STDERR] [+0:02:43] 2025-09-23 21:31:47,491 - interactive_pipeline_01 - WARNING - Preflight skipped: missing Sonarr series id
[2025-09-23 21:31:47] [STDERR] [+0:02:44] 2025-09-23 21:31:47,660 - interactive_pipeline_01 - INFO - Stored enhanced TV metadata for: Batman: The Brave and the Bold (2008)
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] ============================================================
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 📊 TV Download Summary (compact)
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] ============================================================
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] • Batman: The Brave and the Bold (2008) S01E01 → Unknown
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] ============================================================
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 📊 Processing Complete!
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] ============================================================
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 🎬 Movies processed: 0
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 📺 TV shows processed: 1
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 📊 Total content processed: 1
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 🔍 Verifying 1 downloads actually started...
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44]    This replaces guesswork with real verification!
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDERR] [+0:02:44] 2025-09-23 21:31:47,662 - interactive_pipeline_01 - INFO - 🔄 Starting real-time download monitoring (interval: 5s)
[2025-09-23 21:31:47] [STDERR] [+0:02:44] 2025-09-23 21:31:47,664 - interactive_pipeline_01 - INFO - 📦 SABnzbd match found: 'Batman: The Brave and the Bold (2008)' -> 'batman.-.the.brave.and.the.bold.-.s01e01.-.rise.of.the.blue.beetle' (similarity: 1.00)
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] ✅ Download verified: "Batman: The Brave and the Bold (2008)" is now downloading
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDERR] [+0:02:44] 2025-09-23 21:31:47,665 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-23T21:31:47.665398", "event": "download_verified", "job_id": "sonarr_444_series", "title": "Batman: The Brave and the Bold (2008)", "source": "sonarr", "status": "downloading", "progress": 0.0, "size_total": 288599572, "size_downloaded": 0, "speed_bps": 0.0, "eta": "0:00:00", "sonarr_id": 444, "sab_nzo_id": "SABnzbd_nzo_6ub4nii8", "quality": "Unknown"}
[2025-09-23 21:31:47] [STDERR] [+0:02:44] 2025-09-23 21:31:47,759 - interactive_pipeline_01 - INFO - 🔧 Intelligent Fallback System initialized
[2025-09-23 21:31:47] [STDERR] [+0:02:44] 2025-09-23 21:31:47,759 - interactive_pipeline_01 - INFO -    📁 Preflight decisions: workspace\preflight_decisions
[2025-09-23 21:31:47] [STDERR] [+0:02:44] 2025-09-23 21:31:47,760 - interactive_pipeline_01 - INFO -    🎬 Radarr URL: http://localhost:7878
[2025-09-23 21:31:47] [STDERR] [+0:02:44] 2025-09-23 21:31:47,760 - interactive_pipeline_01 - INFO -    🔑 API Key configured: ✅
[2025-09-23 21:31:47] [STDERR] [+0:02:44] 2025-09-23 21:31:47,760 - interactive_pipeline_01 - INFO -    📊 Telemetry integration: ✅
[2025-09-23 21:31:47] [STDERR] [+0:02:44] 2025-09-23 21:31:47,760 - interactive_pipeline_01 - INFO - 🛡️ Intelligent fallback system initialized with telemetry integration
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 📊 Real-time telemetry monitoring started
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 📄 Telemetry dashboard log: logs\telemetry_dashboard_2025-09-23_09-31-47-PM.txt
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:31:47] [STDOUT] [+0:02:44]    (Dashboard output will be written to separate file to keep main log clean)
[2025-09-23 21:31:47] [STDOUT] [+0:02:44] 
[2025-09-23 21:32:18] [STDERR] [+0:03:14] 2025-09-23 21:32:18,066 - interactive_pipeline_01 - INFO - 🔄 Triggering post-processing for completed download: Batman: The Brave and the Bold (2008)
[2025-09-23 21:32:18] [STDERR] [+0:03:14] 2025-09-23 21:32:18,070 - interactive_pipeline_01 - INFO - ✅ Post-processing triggered for: Batman: The Brave and the Bold (2008) (PID: 248644)
