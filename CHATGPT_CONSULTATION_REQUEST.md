# CHATGPT CONSULTATION REQUEST - PlexAutomator Architecture Crisis

## Problem Summary
My PlexAutomator system has a critical architectural flaw causing TV show organization failures. The intake pipeline (Stage 1) is incorrectly storing TV show metadata in the movies table instead of the tv_shows table, breaking the canonical metadata chain to the organization pipeline (Stage 2).

## The Error
When trying to organize "Batman - The Brave and the Bold" TV show, Script 2 fails with:
```
❌ CRITICAL: No canonical metadata found for this content
🚫 Cannot organize content without authoritative metadata from Stage 1
🔍 Sonarr series ID: 444
```

## Root Cause Discovered
**Database Investigation Results:**
- Movies table contains 7 actual movies (correct) + 13 TV shows (WRONG!)
- TV_shows table contains 12 TV shows with proper Sonarr IDs (correct structure)
- "Batman: The Brave and the Bold" exists as Movie ID 20 but missing from tv_shows table
- Missing Sonarr series ID 444 in tv_shows table where Script 2 expects to find it

## Architecture Overview
**Stage 1 (01_intake_and_nzb_search.py)**:
- Processes new movie/TV requests
- Fetches metadata from TMDB/TVDB
- Adds content to Radarr/Sonarr
- **PROBLEM**: Stores TV shows using `save_movie_metadata()` instead of `save_tv_metadata()`

**Stage 2 (02_organize.py)**:
- Monitors completed downloads
- Looks up canonical metadata using `get_tv_metadata_by_sonarr_id()`
- **PROBLEM**: Expects TV metadata in tv_shows table but intake stored it in movies table

## Files You Need to Analyze

### CRITICAL FILES (Please request these):

1. **`_internal/utils/filesystem_first_state_manager.py`**
   - Database management layer
   - Contains `save_movie_metadata()` vs `save_tv_metadata()` methods
   - Has the schema definitions showing table structure differences

2. **`01_intake_and_nzb_search.py`** 
   - 7,122 lines - the intake pipeline
   - **Problem area**: Lines ~4096-4159 where TV shows are incorrectly stored
   - Uses wrong storage method for TV content

3. **`02_organize.py`**
   - 4,764 lines - the organization pipeline  
   - **Failure point**: Lines ~3820-3860 where canonical metadata lookup fails
   - Shows expected behavior and error handling

4. **Database dump** (I can provide this)
   - Shows current corrupted state with TV shows in wrong table
   - Demonstrates the schema differences between tables

## Specific Code Issue Found

**In 01_intake_and_nzb_search.py (around line 4097):**
```python
# THIS IS WRONG - using movie storage for TV shows:
tv_success = metadata_db.save_movie_metadata(  # ← INCORRECT METHOD!
    unique_id=tv_unique_id,
    title=sonarr_result.get("title", raw_tv_title_input),
    year=sonarr_result.get("year"),
    tmdb_id=tv_metadata.get("tmdb_id"),
    imdb_id=tv_metadata.get("imdb_id"),
    metadata=tv_metadata
)

# THIS IS CORRECT - but only sometimes called:
canonical_success = metadata_db.save_tv_metadata(  # ← CORRECT METHOD!
    unique_id=canonical_unique_id,
    title=clean_title,
    year=sonarr_result.get("year"),
    tvdb_id=tv_metadata.get("tvdb_id"),
    tmdb_id=tv_metadata.get("tmdb_id"),
    imdb_id=tv_metadata.get("imdb_id"),
    sonarr_series_id=sonarr_series_id,  # ← THIS IS KEY!
    canonical_title=canonical_title,
    metadata=sonarr_series_metadata
)
```

## What I Need From You

1. **Root Cause Confirmation**: Is my analysis correct about the intake script logic error?

2. **Architecture Fix Strategy**: Should we:
   - Fix intake script to properly detect and store TV shows in correct table?
   - Create migration script to move TV shows from movies to tv_shows table?
   - Implement validation to prevent future cross-table storage issues?

3. **Specific Code Changes**: What exact modifications needed in:
   - Content type detection logic in intake script
   - Storage method selection in intake script
   - Any changes needed in organization script for resilience

4. **Data Migration Plan**: How to safely move existing TV show data from movies table to tv_shows table without breaking anything?

5. **Prevention Mechanisms**: How to ensure this doesn't happen again with future content?

## Current Impact
- All new TV show downloads fail organization
- System worked for 12 shows only because we manually added entries to tv_shows table
- Movies probably still work fine
- Batman and any future TV shows will continue to fail

## Request
Please analyze the provided files and give me:
1. Confirmation of root cause
2. Specific code fixes needed
3. Migration strategy for existing data  
4. Testing plan to validate fixes
5. Prevention measures for future

The system was originally designed with the canonical metadata architecture you suggested, but there's clearly a bug in the content type handling during intake that's breaking the chain of custody between stages.

---
**Files I'm providing separately:**
- TECHNICAL_REPORT_FOR_CHATGPT.md (detailed analysis)
- FILES_FOR_CHATGPT.md (file list and priorities) 
- database_dump_for_chatgpt.sql (current database state)
- Core Python files (filesystem_first_state_manager.py, 01_intake_and_nzb_search.py, 02_organize.py)