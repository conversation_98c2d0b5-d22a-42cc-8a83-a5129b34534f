2025-09-23 21:23:38,519 - ERROR - <PERSON><PERSON><PERSON> reading state file: Expecting value: line 1 column 1 (char 0)
2025-09-23 21:23:48,756 - DEBUG - State saved.
2025-09-23 21:23:48,757 - DEBUG - State saved.
2025-09-23 21:23:48,914 - DEBUG - Imported module for step 03 from C:\Users\<USER>\Videos\PlexAutomator\03_mkv_processor.py
2025-09-23 21:23:48,914 - INFO - No main()/run() in module; executing script as subprocess: C:\Users\<USER>\Videos\PlexAutomator\03_mkv_processor.py
2025-09-23 21:23:48,914 - INFO - Running script with content type argument: --all
