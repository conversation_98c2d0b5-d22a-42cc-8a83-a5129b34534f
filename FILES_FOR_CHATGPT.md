# Files to Provide to ChatGPT for PlexAutomator Architecture Analysis

## Priority 1 - Core Architecture Files (MUST PROVIDE)

### 1. Database Management Layer
**File**: `_internal/utils/filesystem_first_state_manager.py`
**Purpose**: Manages SQLite database with movies and tv_shows tables
**Key Methods**: 
- `save_movie_metadata()` 
- `save_tv_metadata()`
- `get_tv_metadata_by_sonarr_id()`
**Why Critical**: Contains the database schema and storage methods that are being misused

### 2. Stage 1 - Intake Pipeline  
**File**: `01_intake_and_nzb_search.py` (7,122 lines)
**Purpose**: Processes new movie/TV requests, fetches metadata, adds to Radarr/Sonarr
**Key Issue**: Lines 4096-4159 incorrectly use `save_movie_metadata()` for TV shows
**Why Critical**: This is where the wrong table storage is happening

### 3. Stage 2 - Organization Pipeline
**File**: `02_organize.py` (4,764 lines) 
**Purpose**: Monitors downloads, organizes completed content using canonical metadata
**Key Issue**: Lines 3820-3860 expect TV metadata in tv_shows table but intake put it in movies table
**Why Critical**: This is where the failure occurs - shows the expected behavior

## Priority 2 - Integration Layer (SHOULD PROVIDE)

### 4. Radarr Integration
**File**: `_internal/src/radarr_integration.py`
**Purpose**: Handles movie management via Radarr API
**Why Useful**: Shows how movie processing should work

### 5. Sonarr Integration  
**File**: `_internal/src/sonarr_integration.py`
**Purpose**: Handles TV show management via Sonarr API
**Why Useful**: Shows how TV show processing should work and series ID assignment

## Priority 3 - Configuration & Logs (HELPFUL)

### 6. Current Database Export
**Command to generate**:
```bash
sqlite3 "_internal/data/movie_metadata.db" ".dump" > database_dump.sql
```
**Purpose**: Shows current corrupted state with TV shows in movies table

### 7. Settings Configuration
**File**: `_internal/config/settings.ini` (if exists)
**Purpose**: Shows system configuration that might affect content type detection

### 8. Recent Error Log
**File**: Most recent log from `logs/02_organize.log`
**Purpose**: Shows exact error sequence and debugging information

## Priority 4 - Supporting Context (OPTIONAL)

### 9. Metadata API Helpers
**File**: `_internal/utils/metadata_apis.py` (if exists)
**Purpose**: Shows how metadata is fetched and structured

### 10. Common Helpers
**File**: `_internal/utils/common_helpers.py` (if exists) 
**Purpose**: Shows utility functions used across the system

## Specific Code Sections to Highlight

### From `01_intake_and_nzb_search.py`:
```python
# Line ~4097 - THE PROBLEM:
tv_success = metadata_db.save_movie_metadata(  # ← WRONG METHOD!
    unique_id=tv_unique_id,
    title=sonarr_result.get("title", raw_tv_title_input),
    year=sonarr_result.get("year"),
    tmdb_id=tv_metadata.get("tmdb_id"),
    imdb_id=tv_metadata.get("imdb_id"),
    metadata=tv_metadata
)

# Line ~4136 - THE CORRECT METHOD (but only sometimes called):
canonical_success = metadata_db.save_tv_metadata(
    unique_id=canonical_unique_id,
    title=clean_title,
    year=sonarr_result.get("year"),
    tvdb_id=tv_metadata.get("tvdb_id"),
    tmdb_id=tv_metadata.get("tmdb_id"),
    imdb_id=tv_metadata.get("imdb_id"),
    sonarr_series_id=sonarr_series_id,
    canonical_title=canonical_title,
    metadata=sonarr_series_metadata
)
```

### From `02_organize.py`:
```python
# Line ~3822 - THE EXPECTATION:
canonical_metadata = metadata_db.get_tv_metadata_by_sonarr_id(sonarr_series_id)

# Line ~3848 - THE FAILURE:
if not canonical_metadata:
    logger_instance.error(f"❌ CRITICAL: No canonical metadata found for this content")
```

## Questions to Ask ChatGPT

1. **Root Cause**: Why is the intake script storing TV shows in the movies table?

2. **Content Detection**: How should the system properly differentiate between movies and TV shows during intake?

3. **Data Migration**: What's the best way to move existing TV show data from movies table to tv_shows table?

4. **Architecture Fix**: Should we:
   - Fix the intake script to use proper storage methods?
   - Make the organize script check both tables?
   - Implement a hybrid approach?

5. **Prevention**: How can we prevent this type of storage mismatch in the future?

6. **Testing**: What tests should we implement to validate the fix?

## Expected ChatGPT Analysis

ChatGPT should identify:
- The logic error in content type handling during intake
- Specific code changes needed in both intake and organization scripts  
- A migration strategy for existing corrupted data
- Validation mechanisms to prevent future issues
- A testing plan to verify the fixes work

## Current Status Summary for ChatGPT

- **Problem**: TV show "Batman - The Brave and the Bold" fails to organize
- **Cause**: Sonarr series ID 444 exists but no metadata in tv_shows table
- **Discovery**: TV shows incorrectly stored in movies table by intake script
- **Impact**: All new TV show downloads will fail organization
- **Urgency**: Critical - breaks core functionality

This should give ChatGPT everything needed to understand and fix the architectural flaw.