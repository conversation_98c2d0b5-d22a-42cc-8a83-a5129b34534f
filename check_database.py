#!/usr/bin/env python3
"""
Quick database checker to diagnose the metadata issue
"""
import sqlite3
import json
from pathlib import Path

# Database path
db_path = Path(r"C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db")

if not db_path.exists():
    print(f"❌ Database not found at: {db_path}")
    exit(1)

print(f"✅ Database found at: {db_path}")

try:
    conn = sqlite3.connect(str(db_path))
    conn.row_factory = sqlite3.Row  # Enable dict-like access
    
    # Check if tv_shows table exists
    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tv_shows';")
    if cursor.fetchone():
        print("✅ tv_shows table exists")
        
        # Get table schema
        cursor = conn.execute("PRAGMA table_info(tv_shows);")
        columns = cursor.fetchall()
        print(f"\n📋 Table schema ({len(columns)} columns):")
        for col in columns:
            print(f"  - {col['name']}: {col['type']}")
        
        # Check for series ID 444 specifically
        print(f"\n🔍 Checking for Sonarr series ID 444:")
        cursor = conn.execute("SELECT * FROM tv_shows WHERE sonarr_series_id = 444;")
        row = cursor.fetchone()
        if row:
            print("✅ Found metadata for series ID 444:")
            for key in row.keys():
                print(f"  {key}: {row[key]}")
        else:
            print("❌ No metadata found for series ID 444")
        
        # Check if there are any TV shows at all
        cursor = conn.execute("SELECT COUNT(*) as count FROM tv_shows;")
        count = cursor.fetchone()['count']
        print(f"\n📊 Total TV shows in database: {count}")
        
        if count > 0:
            print("\n🔍 All TV shows in database:")
            cursor = conn.execute("SELECT tv_id, title, year, sonarr_series_id, canonical_title FROM tv_shows;")
            rows = cursor.fetchall()
            for row in rows:
                print(f"  ID {row['tv_id']}: '{row['title']}' ({row['year']}) - Sonarr ID: {row['sonarr_series_id']} - Canonical: '{row['canonical_title']}'")
    else:
        print("❌ tv_shows table does not exist")
    
    # Also check movies table to understand the structure better
    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='movies';")
    if cursor.fetchone():
        cursor = conn.execute("SELECT COUNT(*) as count FROM movies;")
        count = cursor.fetchone()['count']
        print(f"\n📊 Total movies in database: {count}")
    
    conn.close()
    
except Exception as e:
    print(f"❌ Error accessing database: {e}")