-- PlexAutomator Database Dump for ChatGPT Analysis
-- Generated: 2025-09-23
-- Issue: TV shows incorrectly stored in movies table

-- SCHEMA DUMP
CREATE TABLE movies (
                    movie_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    unique_id TEXT UNIQUE NOT NULL,  -- e.g. "Title (Year)"
                    title TEXT NOT NULL,
                    year INTEGER,
                    tmdb_id TEXT,
                    imdb_id TEXT,
                    audio_lang TEXT DEFAULT 'eng',
                    subtitle_lang TEXT DEFAULT 'eng',
                    keep_commentary BOOLEAN DEFAULT 0,
                    metadata_json TEXT,  -- Additional metadata as <PERSON><PERSON><PERSON>
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
CREATE TABLE tv_shows (
                    tv_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    unique_id TEXT UNIQUE NOT NULL,  -- e.g. "Show Title (Year)"
                    title TEXT NOT NULL,
                    year INTEGER,
                    tvdb_id TEXT,
                    tmdb_id TEXT,
                    imdb_id TEXT,
                    metadata_json TEXT,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                , sonarr_series_id INTEGER, canonical_title TEXT);
CREATE INDEX idx_movies_unique_id ON movies(unique_id);
CREATE INDEX idx_movies_tmdb_id ON movies(tmdb_id);
CREATE INDEX idx_tv_unique_id ON tv_shows(unique_id);
CREATE INDEX idx_tv_tvdb_id ON tv_shows(tvdb_id);
CREATE INDEX idx_tv_sonarr_id ON tv_shows(sonarr_series_id);

-- DATA DUMP

-- MOVIES TABLE (Contains 7 movies + 13 TV shows - THIS IS THE PROBLEM)
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (1, '13 Going on 30 (2004)', '13 Going on 30', 2004, '10096', 'tt0337563', 'eng', 'eng', 0, '{"success": true, "tmdb_id": 10096, "title": "13 Going on 30", "cleaned_title": "13 Going on 30", "original_title": "13 Going on 30", "release_date": "2004-04-13", "year": 2004, "overview": "After total humiliation at her thirteenth birthday party, Jenna Rink wants to just hide until she''s thirty. Thanks to some magic wishing dust, Jenna''s prayer has been answered. With a knockout body, a fabulous wardrobe, an athlete boyfriend, a dream job, and superstar friends, this can''t be a better life. But soon Jenna realizes that adult life isn\u2019t as easy as she hoped for.", "genres": [{"id": 35, "name": "Comedy"}, {"id": 14, "name": "Fantasy"}, {"id": 10749, "name": "Romance"}], "runtime": 98, "imdb_id": "tt0337563", "poster_path": "/iNZdSIfhSCMtRILDNyhLn8UKeSG.jpg", "backdrop_path": "/snwkqb23cuMd0qY1MPffXWWBoZV.jpg", "vote_average": 6.76, "vote_count": 5339, "popularity": 7.6557, "confidence_score": 97.0, "from_cache": false, "match_metadata": {"elapsed_time": 0.3667142391204834, "match_path": "fast_path"}, "fuzzy_match_warning": null, "matched_via": "enhanced_fuzzy_matching", "original_query": "13 Going on 30 (2004)", "raw_title_input": "13 Going on 30 (2004)", "radarr_id": 691}', '2025-09-22 02:50:35');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (2, 'Don''t Breathe (2016)', 'Don''t Breathe', 2016, '300669', 'tt4160708', 'eng', 'eng', 0, '{"success": true, "tmdb_id": 300669, "title": "Don''t Breathe", "cleaned_title": "Don''t Breathe", "original_title": "Don''t Breathe", "release_date": "2016-06-08", "year": 2016, "overview": "A group of teens break into a blind man''s home thinking they''ll get away with the perfect crime. They''re wrong.", "genres": [{"id": 27, "name": "Horror"}, {"id": 53, "name": "Thriller"}], "runtime": 89, "imdb_id": "tt4160708", "poster_path": "/dSxHyPZ2nipSfvdft4IhQKjk5eZ.jpg", "backdrop_path": "/3mIVOqDbYeucnw09TpjgHvSjXfu.jpg", "vote_average": 7.013, "vote_count": 7703, "popularity": 6.0936, "confidence_score": 97.0, "from_cache": false, "match_metadata": {"elapsed_time": 0.3056187629699707, "match_path": "fast_path"}, "fuzzy_match_warning": null, "matched_via": "enhanced_fuzzy_matching", "original_query": "Don''t Breathe (2016)", "raw_title_input": "Don''t Breathe (2016)", "radarr_id": 692}', '2025-09-22 02:58:47');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (3, 'The Dark Knight (2008)', 'The Dark Knight', 2008, '155', 'tt0468569', 'eng', 'eng', 0, '{"success": true, "tmdb_id": 155, "title": "The Dark Knight", "cleaned_title": "The Dark Knight", "original_title": "The Dark Knight", "release_date": "2008-07-16", "year": 2008, "overview": "Batman raises the stakes in his war on crime. With the help of Lt. Jim Gordon and District Attorney Harvey Dent, Batman sets out to dismantle the remaining criminal organizations that plague the streets. The partnership proves to be effective, but they soon find themselves prey to a reign of chaos unleashed by a rising criminal mastermind known to the terrified citizens of Gotham as the Joker.", "genres": [{"id": 18, "name": "Drama"}, {"id": 28, "name": "Action"}, {"id": 80, "name": "Crime"}, {"id": 53, "name": "Thriller"}], "runtime": 152, "imdb_id": "tt0468569", "poster_path": "/qJ2tW6WMUDux911r6m7haRef0WH.jpg", "backdrop_path": "/enNubozHn9pXi0ycTVYUWfpHZm.jpg", "vote_average": 8.523, "vote_count": 34406, "popularity": 97.0028, "confidence_score": 97.0, "from_cache": false, "match_metadata": {"elapsed_time": 0.329479455947876, "match_path": "fast_path"}, "fuzzy_match_warning": null, "matched_via": "enhanced_fuzzy_matching", "original_query": "The Dark Knight (2008)", "raw_title_input": "The Dark Knight (2008)", "radarr_id": 697}', '2025-09-22 03:36:48');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (4, 'Star Trek Into Darkness (2013)', 'Star Trek Into Darkness', 2013, '54138', 'tt1408101', 'eng', 'eng', 0, '{"success": true, "tmdb_id": 54138, "title": "Star Trek Into Darkness", "cleaned_title": "Star Trek Into Darkness", "original_title": "Star Trek Into Darkness", "release_date": "2013-05-05", "year": 2013, "overview": "When the crew of the Enterprise is called back home, they find an unstoppable force of terror from within their own organization has detonated the fleet and everything it stands for, leaving our world in a state of crisis.  With a personal score to settle, Captain Kirk leads a manhunt to a war-zone world to capture a one man weapon of mass destruction. As our heroes are propelled into an epic chess game of life and death, love will be challenged, friendships will be torn apart, and sacrifices must be made for the only family Kirk has left: his crew.", "genres": [{"id": 28, "name": "Action"}, {"id": 12, "name": "Adventure"}, {"id": 878, "name": "Science Fiction"}], "runtime": 132, "imdb_id": "tt1408101", "poster_path": "/7XrRkhMa9lQ71RszzSyVrJVvhyS.jpg", "backdrop_path": "/k3PqsPLkKLDyjZupZ6gqZHPxMv3.jpg", "vote_average": 7.32, "vote_count": 9342, "popularity": 5.4297, "confidence_score": 97.0, "from_cache": false, "match_metadata": {"elapsed_time": 0.34952783584594727, "match_path": "fast_path"}, "fuzzy_match_warning": null, "matched_via": "enhanced_fuzzy_matching", "original_query": "Star Trek Into Darkness (2013)", "raw_title_input": "Star Trek Into Darkness (2013)", "radarr_id": 696}', '2025-09-22 03:29:16');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (5, 'Top Gun Maverick (2022)', 'Top Gun Maverick', 2022, NULL, NULL, 'eng', 'eng', 0, '{"encoding_preset": "slow", "target_bitrate_kbps": 20000, "encoder_profile": "main10", "encoder_level": "5.1", "turbo_first_pass": false, "handbrake_preset": "H.265 MKV 2160p60 4K", "encoded_file_path": "workspace\\4_ready_for_final_mux\\movies\\4k\\Top Gun Maverick (2022)\\Top Gun Maverick (2022).encoded.mkv", "encoding_timestamp": "2025-09-21T15:06:49.331876", "resolution": "4K"}', '2025-09-21 19:06:49');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (6, 'Top Gun: Maverick (2022)', 'Top Gun: Maverick', 2022, '361743', 'tt1745960', 'eng', 'eng', 0, '{"success": true, "tmdb_id": 361743, "title": "Top Gun: Maverick", "cleaned_title": "Top Gun: Maverick", "original_title": "Top Gun: Maverick", "release_date": "2022-05-21", "year": 2022, "overview": "After more than thirty years of service as one of the Navy\u2019s top aviators, and dodging the advancement in rank that would ground him, Pete \u201cMaverick\u201d Mitchell finds himself training a detachment of TOP GUN graduates for a specialized mission the likes of which no living pilot has ever seen.", "genres": [{"id": 28, "name": "Action"}, {"id": 18, "name": "Drama"}], "runtime": 131, "imdb_id": "tt1745960", "poster_path": "/62HCnUTziyWcpDaBO2i1DX17ljH.jpg", "backdrop_path": "/kBSSbN1sOiJtXjAGVZXxHJR9Kox.jpg", "vote_average": 8.168, "vote_count": 10188, "popularity": 21.4235, "confidence_score": 97.0, "from_cache": false, "match_metadata": {"elapsed_time": 0.3557143211364746, "match_path": "fast_path"}, "fuzzy_match_warning": null, "matched_via": "enhanced_fuzzy_matching", "original_query": "Top Gun: Maverick (2022)", "raw_title_input": "Top Gun: Maverick (2022)", "radarr_id": 694}', '2025-09-22 03:14:06');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (7, 'There Will Be Blood (2007)', 'There Will Be Blood', 2007, '7345', 'tt0469494', 'eng', 'eng', 0, '{"success": true, "tmdb_id": 7345, "title": "There Will Be Blood", "cleaned_title": "There Will Be Blood", "original_title": "There Will Be Blood", "release_date": "2007-12-26", "year": 2007, "overview": "Ruthless silver miner, turned oil prospector, Daniel Plainview, moves to oil-rich California. Using his son to project a trustworthy, family-man image, Plainview cons local landowners into selling him their valuable properties for a pittance. However, local preacher Eli Sunday suspects Plainview''s motives and intentions, starting a slow-burning feud that threatens both their lives.", "genres": [{"id": 18, "name": "Drama"}], "runtime": 158, "imdb_id": "tt0469494", "poster_path": "/fa0RDkAlCec0STeMNAhPaF89q6U.jpg", "backdrop_path": "/9UAKA6ceZi6TgQwTAAMt7DWwYPI.jpg", "vote_average": 8.075, "vote_count": 6982, "popularity": 8.0889, "confidence_score": 97.0, "from_cache": false, "match_metadata": {"elapsed_time": 0.3407421112060547, "match_path": "fast_path"}, "fuzzy_match_warning": null, "matched_via": "enhanced_fuzzy_matching", "original_query": "There Will Be Blood (2007)", "raw_title_input": "There Will Be Blood (2007)", "radarr_id": 695}', '2025-09-22 03:19:44');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (8, 'Ed, Edd n Eddy (1999)', 'Ed, Edd n Eddy', 1999, '77466', 'tt0184111', 'eng', 'eng', 0, '{"success": true, "cleaned_title": "Ed, Edd n Eddy", "year": 1999, "tmdb_id": "606", "tvdb_id": "77466", "imdb_id": "tt0184111", "overview": "Three adolescent boys, Ed, Edd \"Double D\", and Eddy, collectively known as \"the Eds\", constantly invent schemes to make money from their peers to purchase their favorite confectionery, jawbreakers. Their plans usually fail though, leaving them in various predicaments.", "genres": ["Animation", "Comedy", "Kids", "Family"], "status": "Ended", "in_production": false, "number_of_seasons": 6, "number_of_episodes": 126, "networks": ["Cartoon Network"], "metadata_source": "TMDb TV API", "confidence_score": 100.0, "fuzzy_matching_used": true, "raw_title_input": "Ed, Edd n Eddy (1999)", "content_type": "tv_show", "request_specificity": "full_series", "parsed_request": {"request_type": "full_series", "show_title": "Ed, Edd n Eddy", "year": 1999, "seasons": [], "episodes": [], "display_title": "Ed, Edd n Eddy (1999)", "sonarr_params": {"seasons": [1], "monitor_episodes": "season_1_only"}, "requires_tvdb_lookup": true, "chronological_episodes": []}, "sonarr_params": {"seasons": [1], "monitor_episodes": "season_1_only"}}', '2025-09-22 21:32:46');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (9, 'Adventure Time (2010)', 'Adventure Time', 2010, '152831', 'tt1305826', 'eng', 'eng', 0, '{"success": true, "cleaned_title": "Adventure Time", "year": 2010, "tmdb_id": "15260", "tvdb_id": "152831", "imdb_id": "tt1305826", "overview": "Unlikely heroes Finn and Jake are buddies who traverse the mystical Land of Ooo. The best of friends, our heroes always find themselves in the middle of escapades. Finn and Jake depend on each other through thick and thin.", "genres": ["Animation", "Comedy", "Sci-Fi & Fantasy"], "status": "Ended", "in_production": false, "number_of_seasons": 10, "number_of_episodes": 279, "networks": ["Cartoon Network"], "metadata_source": "TMDb TV API", "confidence_score": 100.0, "fuzzy_matching_used": true, "raw_title_input": "Adventure Time (2010)", "content_type": "tv_show", "request_specificity": "full_series", "parsed_request": {"request_type": "full_series", "show_title": "Adventure Time", "year": 2010, "seasons": [], "episodes": [], "display_title": "Adventure Time (2010)", "sonarr_params": {"seasons": [1], "monitor_episodes": "season_1_only"}, "requires_tvdb_lookup": true, "chronological_episodes": []}, "sonarr_params": {"seasons": [1], "monitor_episodes": "season_1_only"}}', '2025-09-23 03:44:25');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (10, 'The Saddle Club (2003)', 'The Saddle Club', 2003, '81993', 'tt0278876', 'eng', 'eng', 0, '{"success": true, "cleaned_title": "The Saddle Club", "year": 2001, "tmdb_id": "38848", "tvdb_id": "81993", "imdb_id": "tt0278876", "overview": "The Saddle Club is a children''s television series based on the books written by Bonnie Bryant Like the book series, the scripted live action series follows the lives of three teenage girls in training to compete in equestrian competitions at the fictional Pine Hollow Stables, while dealing with problems in their personal lives.  Throughout the series, The Saddle Club navigates their rivalry with Veronica, training for competitions, horse shows, and the quotidian dramas that arise between friends and staff in the fictional Pine Hollow Stables. In each show, The Saddle Club prevails over its adversities, usually sending a message emphasizing the importance of friendship and teamwork.", "genres": ["Family", "Drama"], "status": "Ended", "in_production": false, "number_of_seasons": 3, "number_of_episodes": 78, "networks": ["ABC TV"], "metadata_source": "TMDb TV API", "confidence_score": 100.0, "fuzzy_matching_used": true, "raw_title_input": "The Saddle Club (2003)", "content_type": "tv_show", "request_specificity": "full_series", "parsed_request": {"request_type": "full_series", "show_title": "The Saddle Club", "year": 2003, "seasons": [], "episodes": [], "display_title": "The Saddle Club (2003)", "sonarr_params": {"seasons": [1], "monitor_episodes": "season_1_only"}, "requires_tvdb_lookup": true, "chronological_episodes": []}, "sonarr_params": {"seasons": [1], "monitor_episodes": "season_1_only"}}', '2025-09-23 04:12:28');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (11, 'Samurai Jack (2001)', 'Samurai Jack', 2001, '75164', 'tt0278238', 'eng', 'eng', 0, '{"success": true, "cleaned_title": "Samurai Jack", "year": 2001, "tmdb_id": "2723", "tvdb_id": "75164", "imdb_id": "tt0278238", "overview": "A great warrior is displaced to the distant future by the evil shape-shifting wizard Aku. The world has become a bleak place under the rule of Aku, segregated into fantastic tribes and ruled by Aku''s evil robot warlords. Jack travels this foreign landscape in search of a time portal that can return him to his home time so he can \"undo the future that is Aku!\".", "genres": ["Sci-Fi & Fantasy", "Action & Adventure", "Animation"], "status": "Ended", "in_production": false, "number_of_seasons": 5, "number_of_episodes": 60, "networks": ["Cartoon Network", "Adult Swim"], "metadata_source": "TMDb TV API", "confidence_score": 100.0, "fuzzy_matching_used": true, "raw_title_input": "Samurai Jack (2001) S01", "content_type": "tv_show", "request_specificity": "specific_season", "parsed_request": {"request_type": "specific_season", "show_title": "Samurai Jack", "year": 2001, "seasons": [1], "episodes": [], "display_title": "Samurai Jack (2001) S01", "sonarr_params": {"seasons": [1], "monitor_episodes": "season", "requires_tvdb_lookup": true}, "requires_tvdb_lookup": true, "chronological_episodes": []}, "sonarr_params": {"seasons": [1], "monitor_episodes": "season", "requires_tvdb_lookup": true}}', '2025-09-23 04:19:22');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (12, 'Steven Universe (2013)', 'Steven Universe', 2013, '270701', 'tt3061046', 'eng', 'eng', 0, '{"success": true, "cleaned_title": "Steven Universe", "year": 2013, "tmdb_id": "61175", "tvdb_id": "270701", "imdb_id": "tt3061046", "overview": "A young boy takes his mother''s place in a group of gemstone-based beings, and must learn to control his powers.", "genres": ["Animation", "Comedy", "Action & Adventure", "Family", "Sci-Fi & Fantasy"], "status": "Ended", "in_production": false, "number_of_seasons": 5, "number_of_episodes": 154, "networks": ["Cartoon Network"], "metadata_source": "TMDb TV API", "confidence_score": 100.0, "fuzzy_matching_used": true, "raw_title_input": "Steven Universe (2013) S02", "content_type": "tv_show", "request_specificity": "specific_season", "parsed_request": {"request_type": "specific_season", "show_title": "Steven Universe", "year": 2013, "seasons": [2], "episodes": [], "display_title": "Steven Universe (2013) S02", "sonarr_params": {"seasons": [2], "monitor_episodes": "season", "requires_tvdb_lookup": true}, "requires_tvdb_lookup": true, "chronological_episodes": []}, "sonarr_params": {"seasons": [2], "monitor_episodes": "season", "requires_tvdb_lookup": true}}', '2025-09-23 04:32:03');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (13, 'Futurama (1999)', 'Futurama', 1999, '73871', 'tt0149460', 'eng', 'eng', 0, '{"success": true, "cleaned_title": "Futurama", "year": 1999, "tmdb_id": "615", "tvdb_id": "73871", "imdb_id": "tt0149460", "overview": "The adventures of a late-20th-century New York City pizza delivery boy, Philip J. Fry, who, after being unwittingly cryogenically frozen for one thousand years, finds employment at Planet Express, an interplanetary delivery company in the retro-futuristic 31st century.", "genres": ["Animation", "Comedy", "Sci-Fi & Fantasy"], "status": "Returning Series", "in_production": true, "number_of_seasons": 10, "number_of_episodes": 154, "networks": ["FOX", "Comedy Central", "Hulu"], "metadata_source": "TMDb TV API", "confidence_score": 100.0, "fuzzy_matching_used": true, "raw_title_input": "Futurama (1999) S01", "content_type": "tv_show", "request_specificity": "specific_season", "parsed_request": {"request_type": "specific_season", "show_title": "Futurama", "year": 1999, "seasons": [1], "episodes": [], "display_title": "Futurama (1999) S01", "sonarr_params": {"seasons": [1], "monitor_episodes": "season", "requires_tvdb_lookup": true}, "requires_tvdb_lookup": true, "chronological_episodes": []}, "sonarr_params": {"seasons": [1], "monitor_episodes": "season", "requires_tvdb_lookup": true}}', '2025-09-23 04:37:07');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (14, 'The Powerpuff Girls (1998)', 'The Powerpuff Girls', 1998, '76200', 'tt0175058', 'eng', 'eng', 0, '{"success": true, "cleaned_title": "The Powerpuff Girls", "year": 1998, "tmdb_id": "607", "tvdb_id": "76200", "imdb_id": "tt0175058", "overview": "The Powerpuff Girls is a animated television series about Blossom, Bubbles, and Buttercup, three kindergarten-aged girls with superpowers, as well as their \"father\", the brainy scientist Professor Utonium, who all live in the fictional city of Townsville, USA. The girls are frequently called upon by the town''s childlike and naive mayor to help fight nearby criminals using their powers.", "genres": ["Action & Adventure", "Animation", "Comedy", "Kids", "Family"], "status": "Ended", "in_production": false, "number_of_seasons": 6, "number_of_episodes": 135, "networks": ["Cartoon Network"], "metadata_source": "TMDb TV API", "confidence_score": 100.0, "fuzzy_matching_used": true, "raw_title_input": "The Powerpuff Girls (1998)", "content_type": "tv_show", "request_specificity": "full_series", "parsed_request": {"request_type": "full_series", "show_title": "The Powerpuff Girls", "year": 1998, "seasons": [], "episodes": [], "display_title": "The Powerpuff Girls (1998)", "sonarr_params": {"seasons": [1], "monitor_episodes": "season_1_only"}, "requires_tvdb_lookup": true, "chronological_episodes": []}, "sonarr_params": {"seasons": [1], "monitor_episodes": "season_1_only"}}', '2025-09-23 06:26:37');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (15, 'Regular Show (2010)', 'Regular Show', 2010, '188401', 'tt1710308', 'eng', 'eng', 0, '{"success": true, "cleaned_title": "Regular Show", "year": 2010, "tmdb_id": "31132", "tvdb_id": "188401", "imdb_id": "tt1710308", "overview": "The surreal misadventures of two best friends - a blue jay and a raccoon - as they seek to liven up their mundane jobs as groundskeepers at the local park.", "genres": ["Animation", "Comedy"], "status": "Ended", "in_production": false, "number_of_seasons": 8, "number_of_episodes": 245, "networks": ["Cartoon Network"], "metadata_source": "TMDb TV API", "confidence_score": 100.0, "fuzzy_matching_used": true, "raw_title_input": "Regular Show (2010) S08E31", "content_type": "tv_show", "request_specificity": "specific_episodes", "parsed_request": {"request_type": "specific_episodes", "show_title": "Regular Show", "year": 2010, "seasons": [8], "episodes": [{"season": 8, "episode": 31}], "display_title": "Regular Show (2010) S08E31", "sonarr_params": {"seasons": [8], "monitor_episodes": "S08E31", "episodes_list": [{"season": 8, "episode": 31}]}, "requires_tvdb_lookup": false, "chronological_episodes": []}, "sonarr_params": {"seasons": [8], "monitor_episodes": "S08E31", "episodes_list": [{"season": 8, "episode": 31}]}}', '2025-09-23 06:26:57');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (16, 'Dexter''s Laboratory (1996)', 'Dexter''s Laboratory', 1996, '77992', 'tt0115157', 'eng', 'eng', 0, '{"success": true, "cleaned_title": "Dexter''s Laboratory", "year": 1996, "tmdb_id": "4229", "tvdb_id": "77992", "imdb_id": "tt0115157", "overview": "Dexter, a boy-genius with a secret laboratory, constantly battles his sister Dee Dee, who always gains access despite his best efforts to keep her out, as well as his arch-rival and neighbor, Mandark.", "genres": ["Animation", "Comedy", "Sci-Fi & Fantasy"], "status": "Ended", "in_production": false, "number_of_seasons": 4, "number_of_episodes": 220, "networks": ["Cartoon Network"], "metadata_source": "TMDb TV API", "confidence_score": 100.0, "fuzzy_matching_used": true, "raw_title_input": "Dexter''s Laboratory (1996) S01E01, S01E05, S01E12", "content_type": "tv_show", "request_specificity": "multiple_episodes", "parsed_request": {"request_type": "multiple_episodes", "show_title": "Dexter''s Laboratory", "year": 1996, "seasons": [1], "episodes": [{"season": 1, "episode": 1}, {"season": 1, "episode": 5}, {"season": 1, "episode": 12}], "display_title": "Dexter''s Laboratory (1996) S01E01, S01E05, S01E12", "sonarr_params": {"seasons": [1], "monitor_episodes": "specific_episodes", "episodes_list": [{"season": 1, "episode": 1}, {"season": 1, "episode": 5}, {"season": 1, "episode": 12}]}, "requires_tvdb_lookup": false, "chronological_episodes": []}, "sonarr_params": {"seasons": [1], "monitor_episodes": "specific_episodes", "episodes_list": [{"season": 1, "episode": 1}, {"season": 1, "episode": 5}, {"season": 1, "episode": 12}]}}', '2025-09-23 06:28:40');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (17, 'Teen Titans (2003)', 'Teen Titans', 2003, '71503', 'tt0343314', 'eng', 'eng', 0, '{"success": true, "cleaned_title": "Teen Titans", "year": 2003, "tmdb_id": "604", "tvdb_id": "71503", "imdb_id": "tt0343314", "overview": "Fighting for truth, justice and the last slice of pizza, these five superheroes are living proof you''re never too young to save the planet. Protecting Earth and beyond, the Teen Titans use martial arts and gadgetry to battle villains.", "genres": ["Animation", "Action & Adventure", "Sci-Fi & Fantasy"], "status": "Ended", "in_production": false, "number_of_seasons": 5, "number_of_episodes": 65, "networks": ["Cartoon Network"], "metadata_source": "TMDb TV API", "confidence_score": 100.0, "fuzzy_matching_used": true, "raw_title_input": "Teen Titans (2003) S02E03, S02E07, S02E13", "content_type": "tv_show", "request_specificity": "multiple_episodes", "parsed_request": {"request_type": "multiple_episodes", "show_title": "Teen Titans", "year": 2003, "seasons": [2], "episodes": [{"season": 2, "episode": 3}, {"season": 2, "episode": 7}, {"season": 2, "episode": 13}], "display_title": "Teen Titans (2003) S02E03, S02E07, S02E13", "sonarr_params": {"seasons": [2], "monitor_episodes": "specific_episodes", "episodes_list": [{"season": 2, "episode": 3}, {"season": 2, "episode": 7}, {"season": 2, "episode": 13}]}, "requires_tvdb_lookup": false, "chronological_episodes": []}, "sonarr_params": {"seasons": [2], "monitor_episodes": "specific_episodes", "episodes_list": [{"season": 2, "episode": 3}, {"season": 2, "episode": 7}, {"season": 2, "episode": 13}]}}', '2025-09-23 06:32:20');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (18, 'Johnny Bravo (1997)', 'Johnny Bravo', 1997, '76201', 'tt0118360', 'eng', 'eng', 0, '{"success": true, "cleaned_title": "Johnny Bravo", "year": 1997, "tmdb_id": "2405", "tvdb_id": "76201", "imdb_id": "tt0118360", "overview": "Johnny Bravo tells the story of a biceps-bulging, karate-chopping free spirit who believes he is a gift from God to the women of the earth. Unfortunately for Johnny, everyone else sees him as a narcissistic Mama''s boy with big muscles and even bigger hair. In short, he is the quintessential guy who ''just doesn''t get it.'' No matter what he does, or where he finds himself, he always winds up being his own worst enemy.", "genres": ["Animation", "Comedy", "Family"], "status": "Ended", "in_production": false, "number_of_seasons": 4, "number_of_episodes": 181, "networks": ["Cartoon Network"], "metadata_source": "TMDb TV API", "confidence_score": 100.0, "fuzzy_matching_used": true, "raw_title_input": "Johnny Bravo (1997) S01E01, S03E15", "content_type": "tv_show", "request_specificity": "multiple_episodes", "parsed_request": {"request_type": "multiple_episodes", "show_title": "Johnny Bravo", "year": 1997, "seasons": [1, 3], "episodes": [{"season": 1, "episode": 1}, {"season": 3, "episode": 15}], "display_title": "Johnny Bravo (1997) S01E01, S03E15", "sonarr_params": {"seasons": [1, 3], "monitor_episodes": "specific_episodes", "episodes_list": [{"season": 1, "episode": 1}, {"season": 3, "episode": 15}]}, "requires_tvdb_lookup": false, "chronological_episodes": []}, "sonarr_params": {"seasons": [1, 3], "monitor_episodes": "specific_episodes", "episodes_list": [{"season": 1, "episode": 1}, {"season": 3, "episode": 15}]}}', '2025-09-23 06:33:03');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (19, 'Ben 10 (2005)', 'Ben 10', 2005, '79567', 'tt0760437', 'eng', 'eng', 0, '{"success": true, "cleaned_title": "Ben 10", "year": 2005, "tmdb_id": "4686", "tvdb_id": "79567", "imdb_id": "tt0760437", "overview": "When 10-year-old Ben Tennyson discovers a mysterious device, he gains the power to change into ten different alien heroes, each with uniquely awesome powers. With such abilities at his disposal, Ben realizes a greater responsibility to help others and stop evildoers, but that doesn''t mean he''s above a little superpowered mischief now and then.", "genres": ["Animation", "Sci-Fi & Fantasy", "Action & Adventure", "Comedy", "Kids"], "status": "Ended", "in_production": false, "number_of_seasons": 4, "number_of_episodes": 52, "networks": ["Cartoon Network"], "metadata_source": "TMDb TV API", "confidence_score": 100.0, "fuzzy_matching_used": true, "raw_title_input": "Ben 10 (2005) S01E01, S02E13, S04E21", "content_type": "tv_show", "request_specificity": "multiple_episodes", "parsed_request": {"request_type": "multiple_episodes", "show_title": "Ben 10", "year": 2005, "seasons": [1, 2, 4], "episodes": [{"season": 1, "episode": 1}, {"season": 2, "episode": 13}, {"season": 4, "episode": 21}], "display_title": "Ben 10 (2005) S01E01, S02E13, S04E21", "sonarr_params": {"seasons": [1, 2, 4], "monitor_episodes": "specific_episodes", "episodes_list": [{"season": 1, "episode": 1}, {"season": 2, "episode": 13}, {"season": 4, "episode": 21}]}, "requires_tvdb_lookup": false, "chronological_episodes": []}, "sonarr_params": {"seasons": [1, 2, 4], "monitor_episodes": "specific_episodes", "episodes_list": [{"season": 1, "episode": 1}, {"season": 2, "episode": 13}, {"season": 4, "episode": 21}]}}', '2025-09-23 06:36:16');
INSERT INTO movies (movie_id, unique_id, title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, keep_commentary, metadata_json, last_updated) VALUES (20, 'Batman: The Brave and the Bold (2008)', 'Batman: The Brave and the Bold', 2008, '82824', 'tt1213218', 'eng', 'eng', 0, '{"success": true, "cleaned_title": "Batman: The Brave and the Bold", "year": 2008, "tmdb_id": "15804", "tvdb_id": "82824", "imdb_id": "tt1213218", "overview": "The Caped Crusader is teamed up with Blue Beetle, Green Arrow, Aquaman and countless others in his quest to uphold justice.", "genres": ["Animation", "Action & Adventure"], "status": "Ended", "in_production": false, "number_of_seasons": 3, "number_of_episodes": 64, "networks": ["Cartoon Network"], "metadata_source": "TMDb TV API", "confidence_score": 100.0, "fuzzy_matching_used": true, "raw_title_input": "Batman: The Brave and the Bold (2008) S01E01", "content_type": "tv_show", "request_specificity": "specific_episodes", "parsed_request": {"request_type": "specific_episodes", "show_title": "Batman: The Brave and the Bold", "year": 2008, "seasons": [1], "episodes": [{"season": 1, "episode": 1}], "display_title": "Batman: The Brave and the Bold (2008) S01E01", "sonarr_params": {"seasons": [1], "monitor_episodes": "S01E01", "episodes_list": [{"season": 1, "episode": 1}]}, "requires_tvdb_lookup": false, "chronological_episodes": []}, "sonarr_params": {"seasons": [1], "monitor_episodes": "S01E01", "episodes_list": [{"season": 1, "episode": 1}]}}', '2025-09-24 01:31:47');

-- TV_SHOWS TABLE (Correct table but missing Batman series ID 444)
INSERT INTO tv_shows (tv_id, unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json, last_updated, sonarr_series_id, canonical_title) VALUES (1, 'sonarr_433_1758638142', 'Adventure Time', 2010, '152831', NULL, 'tt1305826', '{}', '2025-09-23T10:35:42.881346', 433, 'Adventure Time');
INSERT INTO tv_shows (tv_id, unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json, last_updated, sonarr_series_id, canonical_title) VALUES (2, 'sonarr_443_1758638142', 'Ben 10', 2005, '290434', NULL, 'tt0760437', '{}', '2025-09-23T10:35:42.989340', 443, 'Ben 10');
INSERT INTO tv_shows (tv_id, unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json, last_updated, sonarr_series_id, canonical_title) VALUES (3, 'sonarr_437_1758638143', 'Futurama', 1999, '73871', NULL, 'tt0149460', '{}', '2025-09-23T10:35:43.139976', 437, 'Futurama');
INSERT INTO tv_shows (tv_id, unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json, last_updated, sonarr_series_id, canonical_title) VALUES (4, 'sonarr_435_1758638143', 'Samurai Jack', 2001, '72616', NULL, 'tt0278238', '{}', '2025-09-23T10:35:43.177207', 435, 'Samurai Jack');
INSERT INTO tv_shows (tv_id, unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json, last_updated, sonarr_series_id, canonical_title) VALUES (5, 'sonarr_436_1758638143', 'Steven Universe', 2013, '278018', NULL, 'tt3061046', '{}', '2025-09-23T10:35:43.185697', 436, 'Steven Universe');
INSERT INTO tv_shows (tv_id, unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json, last_updated, sonarr_series_id, canonical_title) VALUES (6, 'sonarr_441_1758638143', 'Teen Titans', 2003, '70478', NULL, 'tt0343314', '{}', '2025-09-23T10:35:43.217159', 441, 'Teen Titans');
INSERT INTO tv_shows (tv_id, unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json, last_updated, sonarr_series_id, canonical_title) VALUES (7, 'sonarr_438_1758638143', 'The Powerpuff Girls', 1998, '77068', NULL, 'tt0175058', '{}', '2025-09-23T10:35:43.253901', 438, 'The Powerpuff Girls');
INSERT INTO tv_shows (tv_id, unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json, last_updated, sonarr_series_id, canonical_title) VALUES (8, 'sonarr_434_1758638143', 'The Saddle Club', 2001, '76290', NULL, 'tt0284717', '{}', '2025-09-23T10:35:43.327513', 434, 'The Saddle Club');
INSERT INTO tv_shows (tv_id, unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json, last_updated, sonarr_series_id, canonical_title) VALUES (9, 'ededdneddy', 'Ed, Edd n Eddy', 1999, '77466', NULL, 'tt0184111', '{"title": "Ed, Edd n Eddy", "alternateTitles": [], "sortTitle": "ed edd n eddy", "status": "ended", "ended": true, "overview": "Three adolescent boys, Ed, Edd \"Double D\", and Eddy, collectively known as \"the Eds\", constantly invent schemes to make money from their peers to purchase their favorite confectionery, jawbreakers. Their plans usually fail though, leaving them in various predicaments.", "previousAiring": "2007-04-29T00:00:00Z", "network": "Cartoon Network", "airTime": "20:00", "images": [{"coverType": "banner", "url": "/MediaCover/432/banner.jpg?lastWrite=638941704453019853", "remoteUrl": "https://artworks.thetvdb.com/banners/graphical/77466-g4.jpg"}, {"coverType": "poster", "url": "/MediaCover/432/poster.jpg?lastWrite=638941704453519015", "remoteUrl": "https://artworks.thetvdb.com/banners/posters/77466-1.jpg"}, {"coverType": "fanart", "url": "/MediaCover/432/fanart.jpg?lastWrite=638941704453831600", "remoteUrl": "https://artworks.thetvdb.com/banners/fanart/original/77466-3.jpg"}, {"coverType": "clearlogo", "url": "/MediaCover/432/clearlogo.png?lastWrite=638941704454124805", "remoteUrl": "https://artworks.thetvdb.com/banners/v4/series/77466/clearlogo/611c16c277b10.png"}], "originalLanguage": {"id": 1, "name": "English"}, "seasons": [{"seasonNumber": 0, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 12, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 1, "monitored": true, "statistics": {"previousAiring": "1999-06-12T00:11:00Z", "episodeFileCount": 0, "episodeCount": 26, "totalEpisodeCount": 26, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 2, "monitored": false, "statistics": {"previousAiring": "2000-12-23T01:11:00Z", "episodeFileCount": 0, "episodeCount": 26, "totalEpisodeCount": 26, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 3, "monitored": false, "statistics": {"previousAiring": "2002-07-13T00:11:00Z", "episodeFileCount": 0, "episodeCount": 25, "totalEpisodeCount": 25, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 4, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 25, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 5, "monitored": false, "statistics": {"previousAiring": "2007-04-29T00:00:00Z", "episodeFileCount": 0, "episodeCount": 22, "totalEpisodeCount": 22, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 6, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 2, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}], "year": 1999, "path": "E:\\Ed, Edd n Eddy", "qualityProfileId": 6, "seasonFolder": true, "monitored": false, "monitorNewItems": "all", "useSceneNumbering": false, "runtime": 11, "tvdbId": 77466, "tvRageId": 3421, "tvMazeId": 6428, "tmdbId": 606, "firstAired": "1999-01-04T00:00:00Z", "lastAired": "2008-06-29T00:00:00Z", "seriesType": "standard", "cleanTitle": "ededdneddy", "imdbId": "tt0184111", "titleSlug": "ed-edd-n-eddy", "rootFolderPath": "E:\\", "certification": "TV-Y7", "genres": ["Animation", "Children", "Comedy", "Family"], "tags": [], "added": "2025-09-22T20:40:44Z", "ratings": {"votes": 43012, "value": 7.4}, "statistics": {"seasonCount": 6, "episodeFileCount": 0, "episodeCount": 99, "totalEpisodeCount": 138, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}, "languageProfileId": 1, "id": 432}', '2025-09-23 14:48:20', 432, 'Ed, Edd n Eddy');
INSERT INTO tv_shows (tv_id, unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json, last_updated, sonarr_series_id, canonical_title) VALUES (10, 'johnnybravo', 'Johnny Bravo', 1997, '76201', NULL, 'tt0118360', '{"title": "Johnny Bravo", "alternateTitles": [], "sortTitle": "johnny bravo", "status": "ended", "ended": true, "overview": "Johnny Bravo tells the story of a biceps-bulging, karate-chopping free spirit who believes he is a gift from God to the women of the earth. Unfortunately for Johnny, everyone else sees him as a narcissistic Mama''s boy with big muscles and even bigger hair. In short, he is the quintessential guy who ''just doesn''t get it.'' No matter what he does, or where he finds himself, he always winds up being his own worst enemy.", "previousAiring": "2000-09-22T04:16:00Z", "network": "Cartoon Network", "images": [{"coverType": "banner", "url": "/MediaCover/442/banner.jpg?lastWrite=638942059512818866", "remoteUrl": "https://artworks.thetvdb.com/banners/graphical/76201-g4.jpg"}, {"coverType": "poster", "url": "/MediaCover/442/poster.jpg?lastWrite=638942059514088202", "remoteUrl": "https://artworks.thetvdb.com/banners/posters/76201-2.jpg"}, {"coverType": "fanart", "url": "/MediaCover/442/fanart.jpg?lastWrite=638942059514820702", "remoteUrl": "https://artworks.thetvdb.com/banners/fanart/original/76201-4.jpg"}, {"coverType": "clearlogo", "url": "/MediaCover/442/clearlogo.png?lastWrite=638942059515702854", "remoteUrl": "https://artworks.thetvdb.com/banners/v4/series/76201/clearlogo/611c2709cfe1e.png"}], "originalLanguage": {"id": 1, "name": "English"}, "seasons": [{"seasonNumber": 0, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 27, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 1, "monitored": false, "statistics": {"previousAiring": "1997-07-07T04:00:00Z", "episodeFileCount": 0, "episodeCount": 1, "totalEpisodeCount": 38, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 2, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 66, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 3, "monitored": false, "statistics": {"previousAiring": "2000-09-22T04:16:00Z", "episodeFileCount": 0, "episodeCount": 1, "totalEpisodeCount": 53, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 4, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 24, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}], "year": 1997, "path": "E:\\Johnny Bravo", "qualityProfileId": 6, "seasonFolder": true, "monitored": false, "monitorNewItems": "all", "useSceneNumbering": false, "runtime": 8, "tvdbId": 76201, "tvRageId": 4066, "tvMazeId": 3932, "tmdbId": 2405, "firstAired": "1997-07-07T00:00:00Z", "lastAired": "2004-08-27T00:00:00Z", "seriesType": "standard", "cleanTitle": "johnnybravo", "imdbId": "tt0118360", "titleSlug": "johnny-bravo", "rootFolderPath": "E:\\", "certification": "TV-Y", "genres": ["Adventure", "Animation", "Children", "Comedy", "Family"], "tags": [], "added": "2025-09-23T06:32:31Z", "ratings": {"votes": 41373, "value": 7.2}, "statistics": {"seasonCount": 4, "episodeFileCount": 0, "episodeCount": 2, "totalEpisodeCount": 208, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}, "languageProfileId": 1, "id": 442}', '2025-09-23 14:48:20', 442, 'Johnny Bravo');
INSERT INTO tv_shows (tv_id, unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json, last_updated, sonarr_series_id, canonical_title) VALUES (11, 'regularshow', 'Regular Show', 2010, '188401', NULL, 'tt1710308', '{"title": "Regular Show", "alternateTitles": [{"title": "Historias corrientes", "seasonNumber": -1}, {"title": "ImE", "seasonNumber": -1}, {"title": "Regular Show - Voellig abgedreht", "seasonNumber": -1}, {"title": "Regular Show (2010)", "seasonNumber": -1, "sceneOrigin": "tvdv", "comment": "PoF"}], "sortTitle": "regular show", "status": "ended", "ended": true, "overview": "The daily surreal adventures of a blue jay and raccoon duo that attempt to deal with their mundane jobs as groundskeepers at the local park.", "network": "Cartoon Network", "airTime": "19:30", "images": [{"coverType": "banner", "url": "/MediaCover/439/banner.jpg?lastWrite=638942056005493319", "remoteUrl": "https://artworks.thetvdb.com/banners/graphical/188401-g2.jpg"}, {"coverType": "poster", "url": "/MediaCover/439/poster.jpg?lastWrite=638942056006444726", "remoteUrl": "https://artworks.thetvdb.com/banners/posters/188401-8.jpg"}, {"coverType": "fanart", "url": "/MediaCover/439/fanart.jpg?lastWrite=638942056007115334", "remoteUrl": "https://artworks.thetvdb.com/banners/fanart/original/188401-4.jpg"}, {"coverType": "clearlogo", "url": "/MediaCover/439/clearlogo.png?lastWrite=638942056007683976", "remoteUrl": "https://artworks.thetvdb.com/banners/v4/series/188401/clearlogo/611b84b80fae1.png"}], "originalLanguage": {"id": 1, "name": "English"}, "seasons": [{"seasonNumber": 0, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 32, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 1, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 12, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 2, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 28, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 3, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 39, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 4, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 37, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 5, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 37, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 6, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 28, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 7, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 36, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 8, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 28, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}], "year": 2010, "path": "E:\\Regular Show", "qualityProfileId": 6, "seasonFolder": true, "monitored": true, "monitorNewItems": "all", "useSceneNumbering": false, "runtime": 11, "tvdbId": 188401, "tvRageId": 26426, "tvMazeId": 868, "tmdbId": 31132, "firstAired": "2010-09-06T00:00:00Z", "lastAired": "2017-01-16T00:00:00Z", "seriesType": "standard", "cleanTitle": "regularshow", "imdbId": "tt1710308", "titleSlug": "regular-show", "rootFolderPath": "E:\\", "certification": "TV-PG", "genres": ["Action", "Adventure", "Animation", "Comedy", "Family", "Fantasy", "Science Fiction"], "tags": [], "added": "2025-09-23T06:26:40Z", "ratings": {"votes": 69107, "value": 8.6}, "statistics": {"seasonCount": 8, "episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 277, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}, "languageProfileId": 1, "id": 439}', '2025-09-23 14:48:39', 439, 'Regular Show');
INSERT INTO tv_shows (tv_id, unique_id, title, year, tvdb_id, tmdb_id, imdb_id, metadata_json, last_updated, sonarr_series_id, canonical_title) VALUES (12, 'dexterslaboratory', 'Dexter''s Laboratory', 1996, '77992', NULL, 'tt0115157', '{"title": "Dexter''s Laboratory", "alternateTitles": [], "sortTitle": "dexters laboratory", "status": "ended", "ended": true, "overview": "Sleep tight, America! Your fate lies safely in the hands of Dexter, a child genius who whips up dazzling, world-saving inventions in his secret laboratory. Big sister Dee Dee frequently wrecks his experiments, but his bigger nemesis is Mandark, his brilliant rival at Huber Elementary School. Mom and Dad, of course, have no idea what their little angel is up to.", "previousAiring": "1996-05-19T17:14:00Z", "network": "Cartoon Network", "airTime": "13:00", "images": [{"coverType": "banner", "url": "/MediaCover/440/banner.jpg?lastWrite=638942056247222629", "remoteUrl": "https://artworks.thetvdb.com/banners/graphical/77992-g2.jpg"}, {"coverType": "poster", "url": "/MediaCover/440/poster.jpg?lastWrite=638942056247750400", "remoteUrl": "https://artworks.thetvdb.com/banners/posters/77992-1.jpg"}, {"coverType": "fanart", "url": "/MediaCover/440/fanart.jpg?lastWrite=638942056248552758", "remoteUrl": "https://artworks.thetvdb.com/banners/fanart/original/77992-6.jpg"}, {"coverType": "clearlogo", "url": "/MediaCover/440/clearlogo.png?lastWrite=638942056249123044", "remoteUrl": "https://artworks.thetvdb.com/banners/v4/series/77992/clearlogo/611bf8e01d15a.png"}], "originalLanguage": {"id": 1, "name": "English"}, "seasons": [{"seasonNumber": 0, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 5, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 1, "monitored": false, "statistics": {"previousAiring": "1996-05-19T17:14:00Z", "episodeFileCount": 0, "episodeCount": 3, "totalEpisodeCount": 38, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 2, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 108, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 3, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 36, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}, {"seasonNumber": 4, "monitored": false, "statistics": {"episodeFileCount": 0, "episodeCount": 0, "totalEpisodeCount": 38, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}}], "year": 1996, "path": "E:\\Dexter''s Laboratory", "qualityProfileId": 6, "seasonFolder": true, "monitored": false, "monitorNewItems": "all", "useSceneNumbering": false, "runtime": 7, "tvdbId": 77992, "tvRageId": 3298, "tvMazeId": 1953, "tmdbId": 4229, "firstAired": "1996-04-28T00:00:00Z", "lastAired": "2003-11-20T00:00:00Z", "seriesType": "standard", "cleanTitle": "dexterslaboratory", "imdbId": "tt0115157", "titleSlug": "dexters-laboratory", "rootFolderPath": "E:\\", "genres": ["Adventure", "Animation", "Children", "Comedy", "Family", "Science Fiction"], "tags": [], "added": "2025-09-23T06:27:04Z", "ratings": {"votes": 56807, "value": 7.9}, "statistics": {"seasonCount": 4, "episodeFileCount": 0, "episodeCount": 3, "totalEpisodeCount": 225, "sizeOnDisk": 0, "releaseGroups": [], "percentOfEpisodes": 0}, "languageProfileId": 1, "id": 440}', '2025-09-23 14:48:39', 440, 'Dexter''s Laboratory');

-- ANALYSIS COMMENTS FOR CHATGPT:
-- 1. Movies table IDs 1-7: Real movies (correct)
-- 2. Movies table IDs 8-20: TV SHOWS (incorrect - should be in tv_shows table)
-- 3. Movie ID 20 is 'Batman: The Brave and the Bold' with no sonarr_series_id
-- 4. TV_shows table has 12 entries with proper sonarr_series_id values
-- 5. Missing: Batman series ID 444 in tv_shows table
-- 6. Movies table missing 'canonical_title' column
-- 7. TV_shows table has proper 'canonical_title' column
