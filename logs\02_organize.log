=== TERMINAL OUTPUT LOG ===
Script: 02_organize
Started: 2025-09-23 21:32:18
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\02_organize.log
==================================================

[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 📝 Terminal logging started for 02_organize
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\02_organize.log
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-23 21:32:18
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] *** UNIFIED Stage 02: Organize ***
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] ==================================================
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] + Consolidated from multiple O2 scripts into one unified implementation
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] >> Modern Radarr API integration
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] -- Simplified workflow: Radarr -> SABnzbd -> Plex
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] >> Clean, maintainable codebase
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDOUT] [+0:00:00]    Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,866 - pipeline_02 - INFO - ===== Starting Pipeline 02 Execution =====
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,868 - pipeline_02 - INFO - Settings loaded successfully
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,868 - pipeline_02 - INFO - Command-line mode: Processing both
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,868 - pipeline_02 - INFO - 🎬 Starting Radarr (Movies) monitoring...
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,869 - pipeline_02 - INFO - ===== Starting Modern Radarr Download Monitoring with SQLite =====
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,869 - pipeline_02 - INFO -      ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,869 - pipeline_02 - INFO - 🔄 Checking for season progression opportunities...
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,870 - pipeline_02 - INFO -      Sequential progression enabled for 0 series
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,870 - pipeline_02 - INFO -      No series opted-in for sequential progression
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,871 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,871 - pipeline_02 - INFO - 🔄 Initializing real-time telemetry for download monitoring...
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,879 - pipeline_02 - INFO - No valid active jobs found in state file
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,879 - pipeline_02 - INFO - No movie candidates found in state file
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,879 - pipeline_02 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,879 - pipeline_02 - INFO - ✅ Real-time telemetry system initialized for download monitoring
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,879 - pipeline_02 - INFO -    🛡️ Intelligent fallback protection: ENABLED
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,879 - pipeline_02 - INFO - Discovering movies by scanning filesystem...
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,907 - pipeline_02 - INFO - Found 24 movies across 14 stages
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,907 - pipeline_02 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,907 - pipeline_02 - INFO -      Radarr API endpoint: http://localhost:7878
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,907 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,914 - pipeline_02 - INFO - Retrieved 0 movies from Radarr
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,915 - pipeline_02 - INFO - No active downloads in Radarr queue
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,941 - pipeline_02 - INFO - Found 0 movies in download states to monitor
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,941 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,965 - pipeline_02 - INFO - Found 0 movies in download states to monitor
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,965 - pipeline_02 - INFO - No movies currently in download states
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,965 - pipeline_02 - INFO -      ENHANCED: Checking both Radarr API and filesystem for completed downloads
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,966 - pipeline_02 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,966 - pipeline_02 - INFO - 🛡️  LONG PATH PRE-PROCESSING: Checking for Windows path length issues...
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,966 - pipeline_02 - INFO -      Found 1 items to check for long paths
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,966 - pipeline_02 - INFO -      Checking folder: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle (103 chars)
[2025-09-23 21:32:18] [STDOUT] [+0:00:00]   DEBUG: Checking folder: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle (139 chars)
[2025-09-23 21:32:18] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,973 - pipeline_02 - INFO -      Long path handling completed - safe to proceed with detection
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,973 - pipeline_02 - INFO - 🔍 Checking for Windows 8.3 short name corruption...
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,974 - pipeline_02 - INFO -      No completed movies found in filesystem
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,974 - pipeline_02 - INFO -      ROBUST DETECTION: Scanning filesystem for completed downloads...
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,974 - pipeline_02 - INFO -      Scanning for completed downloads in: workspace\1_downloading\complete_raw
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,974 - pipeline_02 - INFO -      Content type filter: movie
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,974 - pipeline_02 - INFO -      ENHANCED DETECTION: Scanning for both movies and TV shows...
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,974 - pipeline_02 - INFO -      FILESYSTEM SCAN: Found 0 movies and 0 TV shows
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,974 - pipeline_02 - INFO -      TOTAL CONTENT: 0 items ready for processing
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,974 - pipeline_02 - INFO -      ORGANIZATION SUMMARY: 0 movies organized successfully
[2025-09-23 21:32:18] [STDERR] [+0:00:00] 2025-09-23 21:32:18,976 - pipeline_02 - INFO -      Radarr API: Retrieved 0 movies for status sync
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,002 - pipeline_02 - INFO - Pipeline state refreshed - found 24 movies
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,002 - pipeline_02 - INFO - ✅ Real-time telemetry system cleaned up
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,002 - pipeline_02 - INFO - ===== Finished Modern Radarr Download Monitoring =====
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,002 - pipeline_02 - INFO -     No new completed downloads found this run
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,002 - pipeline_02 - INFO - 📺 Starting Sonarr (TV Shows) monitoring...
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,003 - pipeline_02 - INFO - ===== Starting Modern Sonarr TV Show Download Monitoring with SQLite =====
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,003 - pipeline_02 - INFO -      ENHANCED: Dual-detection system (Filesystem + Sonarr API) + SQLite state
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,003 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,003 - pipeline_02 - INFO - Discovering TV shows by scanning filesystem...
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,028 - pipeline_02 - INFO - Found 24 content items across 14 stages
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,028 - pipeline_02 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,028 - pipeline_02 - INFO -      Sonarr API endpoint: http://localhost:8989
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,028 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent TV show states...
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,031 - pipeline_02 - INFO - Retrieved 1 TV series from Sonarr
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,033 - pipeline_02 - INFO -      Active TV show downloads in Sonarr queue: 1
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,033 - pipeline_02 - INFO -      ENHANCED: Checking both Sonarr API and filesystem for completed TV shows
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,033 - pipeline_02 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,034 - pipeline_02 - INFO - Selected main video file: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle.mp4 (0.24 GB)
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,034 - pipeline_02 - INFO -      Found completed TV folder: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle (1 video files, ~0.24 GB)
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,034 - pipeline_02 - INFO -      FILESYSTEM DETECTION: Found 1 completed TV folders ready for organization
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,034 - pipeline_02 - INFO -      Processing 1 completed TV folders
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,050 - pipeline_02 - INFO -      🔒 Acquired lock for Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle, processing...
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,051 - pipeline_02 - INFO -      Organizing completed TV folder: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,051 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,051 - pipeline_02 - INFO - 🔗 Retrieving authoritative metadata for organization
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,051 - pipeline_02 - INFO - 🔗 Missing sonarr_series_id - applying metadata bridge lookup...
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,051 - pipeline_02 - INFO - 🔍 Processing folder: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,051 - pipeline_02 - INFO - 🔍 Extracted series name: 'Batman - The Brave and the Bold -'
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,051 - pipeline_02 - INFO - 🔍 Looking in preflight dir: workspace\preflight_decisions\tv_shows
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,051 - pipeline_02 - INFO - 🔍 Preflight directory exists, scanning for JSON files...
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,051 - pipeline_02 - INFO - 🔍 Found 0 JSON files: []
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,051 - pipeline_02 - INFO - 🔍 Found 0 matching files for 'batman_-_the_brave_and_the_bold_-': []
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,051 - pipeline_02 - INFO - 🔍 No JSON files found matching series 'batman_-_the_brave_and_the_bold_-'
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,051 - pipeline_02 - INFO - 🔍 No matching preflight JSON found for series: Batman - The Brave and the Bold -
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,052 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,055 - pipeline_02 - INFO - 🔄 Trying Sonarr Parse API for naming variations: 'Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle'
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,059 - pipeline_02 - INFO - 🎯 Sonarr parse API found series ID 444 for 'Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle'
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,059 - pipeline_02 - INFO - ✅ Metadata bridge: Used Sonarr Parse API for naming variation
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,059 - pipeline_02 - INFO - 🎯 Metadata bridge SUCCESS: sonarr_series_id=444 for 'Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle'
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,060 - pipeline_02 - INFO - 🔍 Looking up canonical metadata for Sonarr series ID: 444
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,060 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,060 - pipeline_02 - WARNING - ⚠️ No canonical metadata found for Sonarr series ID 444
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,060 - pipeline_02 - WARNING - 🔄 This may indicate a show added before the metadata bridge was implemented
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,060 - pipeline_02 - ERROR - ❌ CRITICAL: No canonical metadata found for this content
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,060 - pipeline_02 - ERROR - 🚫 Cannot organize content without authoritative metadata from Stage 1
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,060 - pipeline_02 - ERROR - 📋 Content: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,060 - pipeline_02 - ERROR - 🔍 Sonarr series ID: 444
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,060 - pipeline_02 - ERROR - 💡 Solution: Ensure this series was added through the intake pipeline (Stage 1)
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,060 - pipeline_02 - WARNING - ⏸️ Skipping organization - manual intervention required
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,061 - pipeline_02 - INFO -      🔓 Released lock for Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,061 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,061 - pipeline_02 - INFO - 🔄 Checking for dynamic season progression opportunities...
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,061 - pipeline_02 - INFO -      Sequential progression enabled for 0 series
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,061 - pipeline_02 - INFO -      No series opted-in for sequential progression
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,061 - pipeline_02 - INFO - ===== Finished Modern Sonarr TV Show Download Monitoring =====
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,061 - pipeline_02 - INFO -     ✅ Pipeline 02 completed successfully (Movies + TV Shows)
[2025-09-23 21:32:19] [STDERR] [+0:00:00] 2025-09-23 21:32:19,061 - pipeline_02 - INFO - ===== Finished Pipeline 02 Execution =====
[2025-09-23 21:32:19] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-23 21:32:19] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:19] [STDOUT] [+0:00:00] 🏁 Terminal logging ended for 02_organize
[2025-09-23 21:32:19] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:19] [STDOUT] [+0:00:00] 🕐 Ended at: 2025-09-23 21:32:19
[2025-09-23 21:32:19] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:19] [STDOUT] [+0:00:00] ⏱️ Total duration: 0:00:00.289815
[2025-09-23 21:32:19] [STDOUT] [+0:00:00] 
[2025-09-23 21:32:19] [STDOUT] [+0:00:00] 📄 Log saved to: C:\Users\<USER>\Videos\PlexAutomator\logs\02_organize.log
[2025-09-23 21:32:19] [STDOUT] [+0:00:00] 


==================================================
=== TERMINAL OUTPUT LOG END ===
Script: 02_organize
Ended: 2025-09-23 21:32:19
Duration: 0:00:00.289815
==================================================
