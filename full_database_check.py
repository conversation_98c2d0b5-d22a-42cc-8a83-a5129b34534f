#!/usr/bin/env python3
"""
Comprehensive database investigation to understand the current state
"""
import sqlite3
import json
from pathlib import Path

# Database path
db_path = Path(r"C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db")

if not db_path.exists():
    print(f"❌ Database not found at: {db_path}")
    exit(1)

print(f"✅ Database found at: {db_path}")
print("=" * 80)

try:
    conn = sqlite3.connect(str(db_path))
    conn.row_factory = sqlite3.Row  # Enable dict-like access
    
    # 1. Check what tables exist
    print("📋 DATABASE STRUCTURE:")
    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    print(f"Found {len(tables)} tables:")
    for table in tables:
        print(f"  - {table['name']}")
    
    print("\n" + "=" * 80)
    
    # 2. Examine movies table in detail
    print("🎬 MOVIES TABLE ANALYSIS:")
    cursor = conn.execute("PRAGMA table_info(movies);")
    columns = cursor.fetchall()
    print(f"Schema ({len(columns)} columns):")
    for col in columns:
        print(f"  - {col['name']}: {col['type']}")
    
    # Get all movies with detailed info
    cursor = conn.execute("SELECT * FROM movies ORDER BY movie_id;")
    movies = cursor.fetchall()
    print(f"\nFound {len(movies)} movies:")
    
    for movie in movies:
        print(f"\n  Movie ID {movie['movie_id']}:")
        print(f"    Title: '{movie['title']}' ({movie['year'] if movie['year'] else 'Unknown'})")
        print(f"    Unique ID: {movie['unique_id']}")
        print(f"    TMDB ID: {movie['tmdb_id']}")
        print(f"    IMDB ID: {movie['imdb_id']}")
        # Handle missing canonical_title column in movies table
        if 'canonical_title' in movie.keys():
            print(f"    Canonical Title: {movie['canonical_title']}")
        else:
            print(f"    Canonical Title: [Column not found]")
        if movie['metadata_json']:
            try:
                metadata = json.loads(movie['metadata_json'])
                print(f"    Metadata Keys: {list(metadata.keys())}")
            except:
                print(f"    Metadata: Invalid JSON")
        else:
            print(f"    Metadata: None")
    
    print("\n" + "=" * 80)
    
    # 3. Examine TV shows table in detail
    print("📺 TV SHOWS TABLE ANALYSIS:")
    cursor = conn.execute("PRAGMA table_info(tv_shows);")
    columns = cursor.fetchall()
    print(f"Schema ({len(columns)} columns):")
    for col in columns:
        print(f"  - {col['name']}: {col['type']}")
    
    # Get all TV shows with detailed info
    cursor = conn.execute("SELECT * FROM tv_shows ORDER BY tv_id;")
    tv_shows = cursor.fetchall()
    print(f"\nFound {len(tv_shows)} TV shows:")
    
    for show in tv_shows:
        print(f"\n  TV Show ID {show['tv_id']}:")
        print(f"    Title: '{show['title']}' ({show['year']})")
        print(f"    Unique ID: {show['unique_id']}")
        print(f"    Sonarr Series ID: {show['sonarr_series_id']}")
        print(f"    TVDB ID: {show['tvdb_id']}")
        print(f"    TMDB ID: {show['tmdb_id']}")
        print(f"    Canonical Title: {show['canonical_title']}")
        print(f"    Last Updated: {show['last_updated']}")
        if show['metadata_json']:
            try:
                metadata = json.loads(show['metadata_json'])
                print(f"    Metadata Keys: {list(metadata.keys())}")
            except:
                print(f"    Metadata: Invalid JSON")
        else:
            print(f"    Metadata: None")
    
    print("\n" + "=" * 80)
    
    # 4. Check for any other relevant tables
    print("🔍 OTHER TABLES:")
    for table in tables:
        table_name = table['name']
        if table_name not in ['movies', 'tv_shows']:
            cursor = conn.execute(f"SELECT COUNT(*) as count FROM {table_name};")
            count = cursor.fetchone()['count']
            print(f"  {table_name}: {count} rows")
            
            if count > 0 and count < 20:  # Show some data if not too much
                cursor = conn.execute(f"SELECT * FROM {table_name} LIMIT 5;")
                rows = cursor.fetchall()
                print(f"    Sample data:")
                for row in rows:
                    print(f"      {dict(row)}")
    
    conn.close()
    
except Exception as e:
    print(f"❌ Error accessing database: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 80)
print("🔚 Investigation complete!")