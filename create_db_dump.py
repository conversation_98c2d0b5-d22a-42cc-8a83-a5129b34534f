#!/usr/bin/env python3
"""
Create a database dump for ChatGPT analysis
"""
import sqlite3
import json
from pathlib import Path

db_path = Path(r"C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db")

if not db_path.exists():
    print(f"❌ Database not found at: {db_path}")
    exit(1)

output_file = Path("database_dump_for_chatgpt.sql")

try:
    conn = sqlite3.connect(str(db_path))
    
    # Create a comprehensive dump
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- PlexAutomator Database Dump for ChatGPT Analysis\n")
        f.write("-- Generated: 2025-09-23\n")
        f.write("-- Issue: TV shows incorrectly stored in movies table\n\n")
        
        # Dump schema
        f.write("-- SCHEMA DUMP\n")
        for line in conn.iterdump():
            if 'CREATE TABLE' in line or 'CREATE INDEX' in line:
                f.write(line + '\n')
        
        f.write("\n-- DATA DUMP\n\n")
        
        # Dump movies table with analysis
        f.write("-- MOVIES TABLE (Contains 7 movies + 13 TV shows - THIS IS THE PROBLEM)\n")
        cursor = conn.execute("SELECT * FROM movies ORDER BY movie_id")
        rows = cursor.fetchall()
        
        # Get column names
        columns = [description[0] for description in cursor.description]
        
        for row in rows:
            values = []
            for i, val in enumerate(row):
                if val is None:
                    values.append('NULL')
                elif isinstance(val, str):
                    # Escape single quotes
                    escaped = val.replace("'", "''")
                    values.append(f"'{escaped}'")
                else:
                    values.append(str(val))
            
            f.write(f"INSERT INTO movies ({', '.join(columns)}) VALUES ({', '.join(values)});\n")
        
        f.write("\n-- TV_SHOWS TABLE (Correct table but missing Batman series ID 444)\n")
        cursor = conn.execute("SELECT * FROM tv_shows ORDER BY tv_id")
        rows = cursor.fetchall()
        
        # Get column names  
        columns = [description[0] for description in cursor.description]
        
        for row in rows:
            values = []
            for i, val in enumerate(row):
                if val is None:
                    values.append('NULL')
                elif isinstance(val, str):
                    escaped = val.replace("'", "''")
                    values.append(f"'{escaped}'")
                else:
                    values.append(str(val))
            
            f.write(f"INSERT INTO tv_shows ({', '.join(columns)}) VALUES ({', '.join(values)});\n")
        
        # Add analysis comments
        f.write("\n-- ANALYSIS COMMENTS FOR CHATGPT:\n")
        f.write("-- 1. Movies table IDs 1-7: Real movies (correct)\n") 
        f.write("-- 2. Movies table IDs 8-20: TV SHOWS (incorrect - should be in tv_shows table)\n")
        f.write("-- 3. Movie ID 20 is 'Batman: The Brave and the Bold' with no sonarr_series_id\n")
        f.write("-- 4. TV_shows table has 12 entries with proper sonarr_series_id values\n")
        f.write("-- 5. Missing: Batman series ID 444 in tv_shows table\n")
        f.write("-- 6. Movies table missing 'canonical_title' column\n")
        f.write("-- 7. TV_shows table has proper 'canonical_title' column\n")
        
    conn.close()
    print(f"✅ Database dump created: {output_file}")
    
except Exception as e:
    print(f"❌ Error creating dump: {e}")
    import traceback
    traceback.print_exc()