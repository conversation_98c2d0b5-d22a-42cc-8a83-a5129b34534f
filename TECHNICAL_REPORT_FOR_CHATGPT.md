# PlexAutomator Database Architecture Crisis - Technical Analysis Report

## Executive Summary

We have discovered a **critical architectural flaw** in the PlexAutomator system that is causing TV show organization failures. The root cause is that the intake pipeline (Stage 1) is incorrectly storing TV show metadata in the movies table instead of the dedicated tv_shows table, breaking the canonical metadata chain between Stage 1 (intake) and Stage 2 (organize).

## Current System Architecture

### Database Structure
- **Database Location**: `C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db`
- **Tables**: 3 tables (movies, tv_shows, sqlite_sequence)

### Stage Pipeline Flow
1. **Stage 1 (01_intake_and_nzb_search.py)**: Handles new requests, fetches metadata, adds to Radarr/Sonarr
2. **Stage 2 (02_organize.py)**: Monitors downloads, organizes completed content using canonical metadata
3. **Subsequent Stages**: MKV processing, encoding, subtitles, etc.

## The Critical Problem

### Issue Description
The `02_organize.py` script fails with this error for "Batman - The Brave and the Bold":
```
❌ CRITICAL: No canonical metadata found for this content
🚫 Cannot organize content without authoritative metadata from Stage 1
📋 Content: Batman.-.The.Brave.and.the.Bold.-.S01E01.-.Rise.of.the.Blue.Beetle
🔍 Sonarr series ID: 444
💡 Solution: Ensure this series was added through the intake pipeline (Stage 1)
```

### Root Cause Analysis

#### Database Investigation Results
**Movies Table (20 entries):**
- Contains 7 actual movies (IDs 1-7): "13 Going on 30", "Don't Breathe", "The Dark Knight", etc.
- **INCORRECTLY contains 13 TV shows (IDs 8-20)** including all the shows that should be in tv_shows table
- **Missing `canonical_title` column** (shows as [Column not found])
- TV shows have full metadata in `metadata_json` with TMDB/TVDB/IMDB IDs
- **"Batman: The Brave and the Bold" is Movie ID 20** with complete metadata

**TV Shows Table (12 entries):**
- Contains the same 12 TV shows as in movies table (but NOT Batman)
- Has proper `sonarr_series_id` column linking to Sonarr
- Has `canonical_title` column for consistent naming
- Most entries have **empty metadata_json** (showing `[]`)
- Missing **Sonarr series ID 444** (Batman)

#### The Architectural Breakdown
1. **Intake Script Problem**: `01_intake_and_nzb_search.py` is storing TV shows in the movies table
2. **Organization Script Expectation**: `02_organize.py` looks for TV metadata in the tv_shows table
3. **Missing Link**: No connection between intake (movies table) and organization (tv_shows table)

## File Analysis

### Key Files That Need Review

#### 1. Database Schema Management
**File**: `_internal/utils/filesystem_first_state_manager.py`
- Contains `FilesystemFirstStateManager` class
- Manages both movies and tv_shows tables
- Has `get_tv_metadata_by_sonarr_id()` method that's failing
- Schema creation shows different column structures for movies vs tv_shows

#### 2. Intake Pipeline
**File**: `01_intake_and_nzb_search.py` (7,122 lines)
- **Critical Issue Location**: Around lines 4096-4159
- Uses `metadata_db.save_movie_metadata()` for TV shows (WRONG!)
- Should use `metadata_db.save_tv_metadata()` for TV shows
- Has the canonical metadata storage logic but stores in wrong table

#### 3. Organization Pipeline  
**File**: `02_organize.py` (4,764 lines)
- **Error Location**: Lines 3848-3860
- Calls `metadata_db.get_tv_metadata_by_sonarr_id(sonarr_series_id)`
- Expects canonical metadata in tv_shows table
- Fails when metadata is in movies table instead

### Database Method Analysis

#### Correct TV Metadata Storage Method
```python
# In filesystem_first_state_manager.py
def save_tv_metadata(self, unique_id, title, year=None, tvdb_id=None, tmdb_id=None, 
                     imdb_id=None, sonarr_series_id=None, canonical_title=None, metadata=None)
```

#### Incorrect Method Being Used
```python
# In filesystem_first_state_manager.py  
def save_movie_metadata(self, unique_id, title, year=None, tmdb_id=None, imdb_id=None, 
                        audio_lang="en", subtitle_lang="en", keep_commentary=True, metadata=None)
```

## Specific Code Issues Found

### In `01_intake_and_nzb_search.py` (Lines 4096-4159)
```python
# INCORRECT: Using movie metadata storage for TV shows
tv_success = metadata_db.save_movie_metadata(  # ← THIS IS WRONG!
    unique_id=tv_unique_id,
    title=sonarr_result.get("title", raw_tv_title_input),
    year=sonarr_result.get("year"),
    tmdb_id=tv_metadata.get("tmdb_id"),
    imdb_id=tv_metadata.get("imdb_id"),
    metadata=tv_metadata
)

# CORRECT: Should use TV metadata storage
canonical_success = metadata_db.save_tv_metadata(  # ← THIS IS RIGHT!
    unique_id=canonical_unique_id,
    title=clean_title,
    year=sonarr_result.get("year"),
    tvdb_id=tv_metadata.get("tvdb_id"),
    tmdb_id=tv_metadata.get("tmdb_id"),
    imdb_id=tv_metadata.get("imdb_id"),
    sonarr_series_id=sonarr_series_id,
    canonical_title=canonical_title,
    metadata=sonarr_series_metadata
)
```

### In `02_organize.py` (Lines 3820-3860)
```python
# This correctly looks in tv_shows table
canonical_metadata = metadata_db.get_tv_metadata_by_sonarr_id(sonarr_series_id)

if not canonical_metadata:
    logger_instance.error(f"❌ CRITICAL: No canonical metadata found for this content")
    # Fails because metadata is in movies table, not tv_shows table
```

## Previous Fix Attempts Evidence

Looking at the database timestamps:
- **2025-09-23 10:35:42-43**: First 8 shows added to tv_shows table (empty metadata)
- **2025-09-23 14:48:20-39**: Last 4 shows added with full Sonarr metadata

This suggests we manually added entries to the tv_shows table to fix the organization issues, but Batman was never added.

## Impact Analysis

### What's Working
- Movies (IDs 1-7) probably work fine since organize script has movie handling
- TV shows with entries in both tables (IDs 1-12 in tv_shows) work after our manual fixes

### What's Broken
- Any new TV show downloads (like Batman series ID 444)
- The fundamental intake → organization pipeline for TV content
- Canonical metadata chain of custody for TV shows

## Recommended Solution Strategy

### Immediate Fix Options

#### Option 1: Quick Batman Fix
Add Batman entry to tv_shows table with series ID 444

#### Option 2: Architecture Fix
1. Fix intake script to properly categorize movies vs TV shows
2. Use correct storage methods for each content type
3. Create migration script to move TV shows from movies to tv_shows table

#### Option 3: Hybrid Approach
1. Fix Batman immediately
2. Implement proper architecture for future content
3. Gradual migration of existing data

## Files to Provide to ChatGPT

### Core Architecture Files
1. `_internal/utils/filesystem_first_state_manager.py` - Database management
2. `01_intake_and_nzb_search.py` - Stage 1 intake pipeline  
3. `02_organize.py` - Stage 2 organization pipeline

### Supporting Files
4. `_internal/src/radarr_integration.py` - Movie management
5. `_internal/src/sonarr_integration.py` - TV show management
6. Database export of current state
7. Settings/config files that control the pipeline

### Log Files
8. Recent execution logs showing the failure
9. Previous successful runs for comparison

## Technical Questions for ChatGPT

1. How should the intake script properly differentiate between movies and TV shows?
2. What's the best way to migrate existing TV show data from movies to tv_shows table?
3. Should we implement a validation layer to prevent cross-table storage issues?
4. How can we make the system more resilient to these types of metadata storage errors?

## Expected Outcome

ChatGPT should provide:
1. Root cause confirmation of the intake script logic error
2. Specific code fixes for proper content type handling
3. Migration strategy for existing corrupted data
4. Prevention mechanisms for future issues
5. Testing strategy to validate fixes

---

**Status**: Ready for ChatGPT analysis and solution recommendations
**Priority**: Critical - affects all new TV show processing
**Complexity**: Medium - primarily logic fixes with data migration component